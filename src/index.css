@import url("https://fonts.googleapis.com/css2?family=Anton&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Archivo:ital,wght@0,100..900;1,100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  .font-outline-2 {
    -webkit-text-stroke: 2px #ddb743;
  }
  .font-outline-4 {
    -webkit-text-stroke: 4px #deaf2a;
  }
  .font-outline-white-1 {
    -webkit-text-stroke: 1px #ffffff;
  }
  .font-outline-black-1 {
    -webkit-text-stroke: 1px #000002;
  }
  .font-outline-orange-1 {
    -webkit-text-stroke: 1px #e46526;
  }
  .animate-blink {
    animation: blink 1.5s infinite linear;
  }
  .sparkle {
    position: absolute;
    clip-path: polygon(
      100% 50%,
      78.98% 57.76%,
      93.3% 75%,
      71.21% 71.21%,
      75% 93.3%,
      57.76% 78.98%,
      50% 100%,
      42.24% 78.98%,
      25% 93.3%,
      28.79% 71.21%,
      6.7% 75%,
      21.02% 57.76%,
      0% 50%,
      21.02% 42.24%,
      6.7% 25%,
      28.79% 28.79%,
      25% 6.7%,
      42.24% 21.02%,
      50% 0%,
      57.76% 21.02%,
      75% 6.7%,
      71.21% 28.79%,
      93.3% 25%,
      78.98% 42.24%
    );
    background: #eb9261;
    color: #eb9261;
    border-radius: 50%; /* Circle shape */
    /* mask-image: radial-gradient(circle, #000 71%, transparent 72%); Circular mask effect */
    /* -webkit-mask-image: radial-gradient(circle, #000 71%, transparent 72%); For Webkit browsers */
    animation: blink 1.5s infinite;
  }
  .sparkle1 {
    position: absolute;
    mask: radial-gradient(#0000 71%, #000 72%) 10000% 10000%/99.5% 99.5%;
    background: #eb9261;
    color: #eb9261;
    /* border-radius: 50%; Circle shape */
    /* mask-image: radial-gradient(circle, #000 71%, transparent 72%); Circular mask effect */
    /* -webkit-mask-image: radial-gradient(circle, #000 71%, transparent 72%); For Webkit browsers */
    animation: blink 1.5s infinite;
  }

  /* Animation for blinking effect */
  @keyframes blink {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }

  @layer utilities {
    .font-outline-1 {
      -webkit-text-stroke: 1px black;
      color: transparent; /* Makes the inner text transparent */
    }
  }

  .scrolling-container {
    position: relative;
    overflow: hidden; /* Prevent horizontal overflow */
    width: 100%; /* Ensure it doesn't exceed the viewport width */
  }

  .scrolling-content {
    display: inline-block;
    white-space: nowrap; /* Ensure content stays in a single line */
    animation: scroll-horizontal 10s linear infinite; /* Adjust duration as needed */
  }

  @keyframes scroll-horizontal {
    0% {
      transform: translateX(100%); /* Start from the right */
    }
    100% {
      transform: translateX(-100%); /* End at the left */
    }
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 0px;
}

/* ::-webkit-scrollbar-track {
  background: #f1f1f1;
} */

/* ::-webkit-scrollbar-thumb {
  background-color: #ea9368; 
  border-radius: 10px;     
 
} */

/* ::-webkit-scrollbar-thumb:hover {
  background-color: #f07f47; 
} */

/* Initial outline class with a thinner stroke */
.font-outline-black-11 {
  -webkit-text-stroke: 1px black;
  color: black; /* Initial text is solid black */
}

/* Hover outline class with thicker stroke and border effect */
.hover\:font-outline-black-2:hover {
  -webkit-text-stroke: 2px black;
  color: transparent; /* Makes the text itself transparent */
  position: relative; /* Required for border effect */
}

.hover\:font-outline-black-2:hover::before {
  content: attr(data-text); /* Use the same text for the border effect */
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  -webkit-text-stroke: 3px black;
  filter: blur(2px); /* Adds a soft blur to the border */
  color: transparent; /* Ensures the border doesn't have a solid fill */
}

.text-special {
  text-shadow: 8px 8px 0px rgba(0, 0, 0, 1);
}

@media (max-width: 640px) {
  .text-special {
    text-shadow: 5px 5px 2px rgba(0, 0, 0, 0.7),
    0px 2px 1px black,1px 0px 3px black; /* Additional black outline */
   
  }
}


