import { cn } from "@/lib/utils";
import Marquee from "../components/ui/marquee";
// import Marquee from "./components/magicui/marquee";
import bghome from "../assets/bgHome.avif";
import LineGroup from "../assets/LineGroup.png";
import EdgeLineGroup from "../assets/EdgeLineGroup.png";
import Logo from "../assets/uv_white_logo-removebg.png";
import { VelocityScroll } from "../components/ui/scroll-based-velocity";
import { DiscoverEvents } from "@/components/DiscoverEvent/DiscoverEvent";
import { HomeSliderText } from "@/components/HomeSliderText/HomeSliderText";
import WhichVenueEvent from "@/components/WhichVenueEvent/WhichVenueEvent";
import FollowUs from "@/components/Followus/Followus";
import TeamService from "@/components/TeamService/TeamService";
import Footer from "@/components/Footer/Footer";
import bparty from "../assets/HomeLatest.png";
import poolparty from "../assets/poolparty.png";
import home2 from "../assets/haldi.png";
import home3 from "../assets/mehndiMarqee.jpg";
import wedding from "../assets/wedding.png";
import eco from "../assets/discover5.png";
import secondsection from "../assets/secondsection2.png";
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import Schedule from "@/components/FoodOrder/Schedule";
import CustomFooter from "@/components/Footer/CustomFooter";
import NotificationBadge from "@/components/NotificationBadge/NotificationBadge";

const reviews = [
  {
    name: "POOL PARTY",
    username: "",
    body: "",
    img: poolparty,
  },
  {
    name: "OFFICE PARTY",
    username: "",
    body: "",
    img: bparty,
  },
  {
    name: "HALDI FUNCTION",
    username: "",
    body: "",
    img: home2,
  },
  {
    name: "MEHNDI FUNCTION",
    username: "",
    body: "",
    img: home3,
  },
  {
    name: "WEDDING FUNCTION",
    username: "",
    body: "",
    img: wedding,
  },
  // {
  //   name: "ECO FRIENDLY PARTIE'S",
  //   username: "",
  //   body: "",
  //   img: eco,
  // },
];

const ReviewCard = ({ img, name, username, body, onHover }) => {
  // Check if name is present and other parameters are empty
  const isNameOnly = name && !username && !body;

  return (
    <figure
      onMouseEnter={() => onHover(img, name)}
      onMouseLeave={() => onHover(null, null)}
      className="relative h-100 w-45 cursor-pointer overflow-hidden rounded-xl "
    >
      <div
        className="flex items-center justify-center h-[310px] mt-10  text-6xl  w-full transform rotate-90 font-bold "
        style={{
          color: "#fcf2e9",
          textShadow: `
            -1px -1px 0 #be724d,
            1px -1px 0 #be724d,
            -1px 1px 0 #be724d,
            1px 1px 0 #be724d
          `,
        }}
      >
        {name}
      </div>
      {img && (
        <img
          className="rounded-full object-cover w-[60%] h-80   "
          alt={name}
          src={img}
        />
      )}
    </figure>
  );
};

export function MarqueeDemoVertical({ handleHover }) {
  const firstRow = reviews.slice(0, Math.ceil(reviews.length / 1));
  const secondRow = reviews.slice(0, Math.ceil(reviews.length / 1));

  return (
    <div className="relative flex h-full w-full flex-row items-center justify-center overflow-hidden border bg-homebg md:shadow-xl">
      <Marquee pauseOnHover vertical className="[--duration:20s]">
        {firstRow.map((review, index) => (
          <ReviewCard
            key={`firstRow-${index}`}
            {...review}
            onHover={handleHover}
          />
        ))}
      </Marquee>
      <Marquee reverse pauseOnHover vertical className="[--duration:20s]">
        {secondRow.map((review, index) => (
          <ReviewCard
            key={`secondRow-${index}`}
            {...review}
            onHover={handleHover}
          />
        ))}
      </Marquee>
      <div className="pointer-events-none absolute inset-x-0 top-0 h-1/4 bg-gradient-to-b from-white dark:from-background"></div>
    </div>
  );
}

function Home() {
  const [hoverData, setHoverData] = useState({
    img: poolparty, // Default background image
    name: "POOL PARTY", // Default name
  });
  const [currentIndex, setCurrentIndex] = useState(0);
  const handleHover = (img, name) => {
    setHoverData({ img: img || poolparty, name: name || "POOL PARTY" });
  };
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % reviews.length);
      setHoverData(reviews[(currentIndex + 1) % reviews.length]);
    }, 3000);

    return () => clearInterval(interval);
  }, [currentIndex]);
  return (
    <>
      <div className="flex flex-col lg:flex-row h-screen w-full bg-homebg">
        {/* Logo Section */}
        <div>
          <img
            src={Logo}
            className="absolute z-10 top-4 left-4 w-[10rem] lg:w-30 "
            alt="logo"
          />
        </div>

        {/* Background Image Section with Layer */}
        <div
          className="relative w-full lg:w-[50vw] h-[50vh] lg:h-[100vh] bg-cover bg-center flex flex-col justify-center items-center"
          style={{
            backgroundImage: `url(${hoverData.img})`,
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }}
        >
          {/* Overlay Layer */}
          <div className="absolute inset-0 bg-black opacity-50"></div>

          {/* Content on Top of Background */}
          <h2
            style={{
              transform: "rotateX(-28deg) rotateY(16deg)",
              textShadow: "8px 8px 0px rgba(0,0,0,1)",
            }}
            className="font-Anton text-[2.5rem] sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl text-white z-20 text-center px-2 leading-tight"
          >
            {hoverData.name}
          </h2>

          <button
            style={{
              transform: "rotateX(-28deg) rotateY(16deg)",
            }}
            className="relative uppercase shadow-custom px-4 py-2 md:px-6 md:py-3 lg:px-8 lg:py-2 bg-white border border-black font-bold rounded-sm mt-4 z-20 transition-all duration-300 hover:scale-105 hover:shadow-xl hover:-translate-y-1 group"
          >
            <Link to="/venue">View All Venues</Link>
          </button>
        </div>

        {/* Marquee Section */}
        <div className="w-full lg:w-[50vw] h-[50vh] lg:h-[100vh] flex justify-center items-center">
          <MarqueeDemoVertical handleHover={handleHover} />
        </div>
      </div>

      <div className="flex flex-col h-auto pb-20 w-full bg-homebg relative justify-center items-center pt-10 lg:pt-10">

        <WhichVenueEvent />
        <img
          className="absolute top-[-2rem] lg:-top-20 md:w-[100%] 2xl:w-[100%]  lg:w-auto"
          src={LineGroup}
          alt=""
        />

      </div>

      <DiscoverEvents />
      {/*...for future comments...*/}
      {/* <HomeSliderText /> */}
      {/* <WhichVenueEvent /> */}

      <div className="bg-[#fcf2e9]">
        <div className=" sm:top-20 bg-[#fcf2e9] top-[2rem] md:top-30 lg:top-40 pt-10 mt-0 text-center z-20 px-4 flex flex-col lg:flex-row items-center justify-center">
          <h2 className="font-Anton text-[3rem] md:text-7xl lg:text-9xl text-white sm:mr-7 text-special">
            MAKE MOMENT SPECIAL
          </h2>
        </div>

        <div className="w-full bg-[#fcf2e9] flex flex-col lg:flex-row items-center lg:justify-between px-4 lg:px-10 z-10 mt-20 lg:mt-50">
          <img
            className="w-full mt-0 mb-20
    sm:w-[75%] 
    md:w-[60%] 
    lg:w-[50%] 
    max-h-[73%] 
    rounded-tl-[360px] 
    rounded-tr-[360px] 
    object-cover"
            src={secondsection}
            alt=""
          />
          <div className="w-full max-w-md lg:max-w-[40%] h-auto mt-10 lg:mt-10 flex flex-col justify-center">
            <h2 className="font-Anton text-lg md:text-2xl lg:text-4xl leading-[1.6] tracking-[1px] text-center lg:text-left">
              Find Your Perfect Venue
            </h2>
            <p className="font-Archivo font-normal mt-4 tracking-[1px] text-sm md:text-xl text-center lg:text-left mb-[50px]">
              Discover top venues for weddings, parties, and special
              events—tailored to your needs!
            </p>

            <Link to="/venue">
              <button className="relative shadow-custom px-8 py-2 bg-white border border-black font-normal tracking-wide rounded-sm font-Anton transition-all duration-300 hover:scale-105 hover:shadow-xl hover:-translate-y-1 group ">
                ALL VENUES
              </button>
            </Link>
          </div>
        </div>
      </div>

      <TeamService />
      <Schedule />
      <FollowUs />
      <Footer />
      <div className="z-50 fixed">
        <CustomFooter />
      </div>

      <NotificationBadge notificationCount={1} />
    </>
  );
}

export default Home;
