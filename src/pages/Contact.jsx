import React, { useState } from "react";
import SparklesText from "../components/ui/sparkles-text";
import Footer from "@/components/Footer/Footer";
import Logo from "../assets/urbanvenuelogo.png";
import { Link } from "react-router-dom";
import CustomFooter from "@/components/Footer/CustomFooter";

const Contact = () => {
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    currentLocation: "",
    desiredLocation: "Noida",
    occasion: "OFFICE PARTY",
    message: "",
  });

  const [errors, setErrors] = useState({});

  const validate = () => {
    const newErrors = {};
    if (!formData.fullName.trim())
      newErrors.fullName = "Full Name is required.";
    if (!formData.email.trim()) {
      newErrors.email = "Email is required.";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is not valid.";
    }
    if (!formData.phone.trim()) {
      newErrors.phone = "Phone Number is required.";
    } else if (!/^\d{10}$/.test(formData.phone)) {
      newErrors.phone = "Phone Number must be 10 digits.";
    }
    if (!formData.currentLocation.trim())
      newErrors.currentLocation = "Current location is required.";
    if (!formData.message.trim()) newErrors.message = "Message is required.";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.id]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validate()) {
      alert("Form submitted successfully!");
      setFormData({
        fullName: "",
        email: "",
        phone: "",
        currentLocation: "",
        desiredLocation: "Noida",
        occasion: "OFFICE PARTY",
        message: "",
      });
    }
  };

  return (
    <div className="w-full bg-[#fcf2e9] overflow-x-hidden flex flex-col justify-center items-center py-4 ">
      <Link to="/" className="absolute top-4 left-4 z-10">
        <img src={Logo} className="w-[10rem] lg:w-30" alt="" />
      </Link>
      ;
      <div className="flex flex-col justify-center items-center w-[80%] my-20 ">
        <SparklesText
          className="font-Anton text-7xl text-white font-outline-black-1 my-4 "
          text="CONTACT"
          text2="US"
        />
        <p className="font-Archivo tracking-wide font-semibold text-[#4d4844]">
          Fill out this form, our team will get back to you as soon as possible!
        </p>
        <br />

        {/* Updated Grid to Single Column */}
        <form
          onSubmit={handleSubmit}
          className="grid grid-cols-2 items-center gap-4 w-full py-4"
        >
          <div>
            <label htmlFor="fullName" className="block mb-1 font-medium">
              Full Name
            </label>
            <input
              type="text"
              id="fullName"
              value={formData.fullName}
              onChange={handleChange}
              className={`w-full p-2 border rounded ${
                errors.fullName ? "border-red-500" : ""
              }`}
            />
            {errors.fullName && (
              <p className="text-red-500 text-sm">{errors.fullName}</p>
            )}
          </div>

          <div>
            <label htmlFor="email" className="block mb-1 font-medium">
              Email
            </label>
            <input
              type="email"
              id="email"
              value={formData.email}
              onChange={handleChange}
              className={`w-full p-2 border rounded ${
                errors.email ? "border-red-500" : ""
              }`}
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{errors.email}</p>
            )}
          </div>

          <div>
            <label htmlFor="phone" className="block mb-1 font-medium">
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              value={formData.phone}
              onChange={handleChange}
              className={`w-full p-2 border rounded ${
                errors.phone ? "border-red-500" : ""
              }`}
            />
            {errors.phone && (
              <p className="text-red-500 text-sm">{errors.phone}</p>
            )}
          </div>

          <div>
            <label htmlFor="currentLocation" className="block mb-1 font-medium">
              Your current location
            </label>
            <input
              type="text"
              id="currentLocation"
              value={formData.currentLocation}
              onChange={handleChange}
              className={`w-full p-2 border rounded ${
                errors.currentLocation ? "border-red-500" : ""
              }`}
            />
            {errors.currentLocation && (
              <p className="text-red-500 text-sm">{errors.currentLocation}</p>
            )}
          </div>

          <div>
            <label htmlFor="desiredLocation" className="block mb-1 font-medium">
              Location you are looking for
            </label>
            <select
              id="desiredLocation"
              value={formData.desiredLocation}
              onChange={handleChange}
              className="w-full p-2 border rounded"
            >
              <option>Noida</option>
              <option>New Delhi</option>
              <option>Gurugram</option>
            </select>
          </div>

          <div>
            <label htmlFor="occasion" className="block mb-1 font-medium">
              Occasion
            </label>
            <select
              id="occasion"
              value={formData.occasion}
              onChange={handleChange}
              className="w-full p-2 border rounded"
            >
              <option>OFFICE PARTY</option>
              <option>HALDI PARTY</option>
              <option>MEHNDI PARTY</option>
              <option>WEDDING PARTY</option>
              <option>ECO FRIENDLY PARTIE'S</option>
              <option>OTHER</option>
            </select>
          </div>

          <div className="col-span-2">
            <label htmlFor="message" className="block mb-1 font-medium">
              Any Special Requirements?
            </label>
            <textarea
              id="message"
              value={formData.message}
              onChange={handleChange}
              rows="8"
              className={`w-full p-2 border rounded ${
                errors.message ? "border-red-500" : ""
              }`}
            />
            {errors.message && (
              <p className="text-red-500 text-sm">{errors.message}</p>
            )}
          </div>

          <div className="col-span-2 flex justify-center">
            <button
              type="submit"
              className="  relative shadow-custom px-8 py-2 bg-white border border-black font-normal tracking-wide rounded-sm font-Anton transition-all duration-300 hover:scale-105 hover:shadow-xl hover:-translate-y-1 group "
            >
              Send
            </button>
          </div>
        </form>
      </div>
      <Footer />
      <div className="z-50 fixed">
      <CustomFooter />
      </div>
    </div>
  );
};

export default Contact;
