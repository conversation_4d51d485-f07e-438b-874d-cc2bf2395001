import React, { useState, useEffect } from "react";
import { EventDetailslider } from "@/components/EventDetailSlider/EventDetailslider";
import WhatOurClientSay from "@/components/WhatOurClientSay/WhatOurClientSay";
import SliderEventDetails from "@/components/SliderEventDetails/SliderEventDetails";
import Followus from "@/components/Followus/Followus";
import Footer from "@/components/Footer/Footer";
import { useLocation, Link, useParams, useNavigate } from "react-router-dom";
import Logo from "../assets/uv_white_logo-removebg.png";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import WhichVenueEvent from "@/components/WhichVenueEvent/WhichVenueEvent";
import emailjs from "emailjs-com";
import CustomFooter from "@/components/Footer/CustomFooter";
import { fetchAllEvents, processEventData } from "../services/api.js";
import data from "../assets/EventsData.js"; // Fallback data
import LazyImage from "@/components/ui/LazyImage";
import { batchPreloadImages } from "../utils/imageOptimization";

// Add modal animation styles
const modalStyles = `
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(10px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }
`;

const EventsDetails = () => {
  // Add animation styles to document head
  useEffect(() => {
    // Create style element
    const styleEl = document.createElement("style");
    styleEl.innerHTML = modalStyles;
    document.head.appendChild(styleEl);

    // Cleanup on component unmount
    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);

  // Get ID from URL params
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  // State for event data
  const [eventData, setEventData] = useState({});
  const [dataLoading, setDataLoading] = useState(false);
  const [dataError, setDataError] = useState(null);

  // Extract data from location.state if available
  const stateData = location.state || {};

  // Get data from state or fetched data, but don't create new variables that will change on every render
  // We'll use stateData and eventData directly in the JSX instead

  // Force hide loading screen after a timeout
  useEffect(() => {
    // Set a timeout to hide the loading screen after 1.5 seconds
    const timer = setTimeout(() => {
      setDataLoading(false);
      setMainImageLoading(false);
    }, 1500);

    // Clean up the timer
    return () => clearTimeout(timer);
  }, []);

  // Fetch event data if needed (when navigating directly to the URL)
  useEffect(() => {
    // Create a flag to track if the component is mounted
    let isMounted = true;

    const fetchEventData = async () => {
      // Log state for debugging
      console.log("Current state data:", stateData);
      console.log("Current ID from URL:", id);

      // ALWAYS fetch data when there's an ID in the URL for direct navigation
      if (id && isMounted) {
        try {
          setDataLoading(true);
          console.log("Fetching event data for ID:", id);

          console.log("Attempting to fetch all events...");

          // Fetch all events
          const response = await fetchAllEvents();
          console.log("Events API response received:", response ? "Success" : "Failed");

          const processedData = processEventData(response);
          console.log("Processed event data:", processedData ? "Success" : "Failed");

          // SIMPLIFIED APPROACH: Just get the first event from any category
          let foundEvent = null;

          // Parse the ID from the URL to extract components
          const idParts = id.split('-');
          const targetEventId = idParts[idParts.length - 1]; // Last part should be the numeric ID
          console.log("Looking for event with ID:", targetEventId);

          // First try to find an event that matches part of the ID
          if (processedData && processedData.images) {
            console.log("Available cities:", Object.keys(processedData.images));

            let exactMatch = false;

            // Loop through all cities and event types
            for (const city of Object.keys(processedData.images)) {
              if (exactMatch) break;

              console.log(`Checking city: ${city}`);
              for (const type of Object.keys(processedData.images[city])) {
                console.log(`Checking type: ${type} in city: ${city}`);

                const events = processedData.images[city][type];
                if (events && events.length > 0) {
                  // Look for an exact match by ID first
                  for (const event of events) {
                    if (event.id && event.id.toString() === targetEventId) {
                      console.log("Found EXACT matching event by ID:", event.alt || "Unnamed event");
                      foundEvent = event;
                      exactMatch = true;
                      break;
                    }
                  }

                  // If we found an exact match, break out
                  if (exactMatch) break;

                  // Otherwise, just take the first event as a fallback
                  if (!foundEvent) {
                    foundEvent = events[0];
                    console.log("Found a fallback event to display:", foundEvent.alt || "Unnamed event");
                  }
                }
              }
            }
          } else {
            console.error("No processed data available from API, trying fallback data");

            // Try to use fallback data
            if (data && data.images) {
              console.log("Using fallback data");

              let exactMatch = false;

              // Loop through all cities and event types in fallback data
              for (const city of Object.keys(data.images)) {
                if (exactMatch) break;

                console.log(`Checking fallback city: ${city}`);
                for (const type of Object.keys(data.images[city])) {
                  console.log(`Checking fallback type: ${type} in city: ${city}`);

                  const events = data.images[city][type];
                  if (events && events.length > 0) {
                    // Look for an exact match by ID first
                    for (const event of events) {
                      if (event.id && event.id.toString() === targetEventId) {
                        console.log("Found EXACT matching event by ID in fallback data:", event.alt || "Unnamed event");
                        foundEvent = event;
                        exactMatch = true;
                        break;
                      }
                    }

                    // If we found an exact match, break out
                    if (exactMatch) break;

                    // Otherwise, just take the first event as a fallback
                    if (!foundEvent) {
                      foundEvent = events[0];
                      console.log("Found a fallback event to display:", foundEvent.alt || "Unnamed event");
                    }
                  }
                }
              }
            } else {
              console.error("No fallback data available either");
            }
          }

          if (foundEvent) {
            console.log("Found matching event:", foundEvent);

            // Process image URL
            const imageUrl = foundEvent.src && foundEvent.src.startsWith("/")
              ? `http://localhost:9000${foundEvent.src}`
              : foundEvent.src;

            // Process subImages to ensure proper URLs
            const processedSubImages = foundEvent.subImages?.map((img) => ({
              ...img,
              src: img.src && img.src.startsWith("/")
                ? `http://localhost:9000${img.src}`
                : img.src,
            })) || [];

            // Set the event data
            setEventData({
              image: imageUrl,
              title: foundEvent.alt,
              subImages: processedSubImages,
              info: foundEvent.info || [],
              videos: foundEvent.videos || [],
              hasVideo: foundEvent.hasVideo || false
            });

            setDataError(null);
          } else {
            console.error("No event found in API data");

            // Show a permanent error message without redirecting
            setDataError("We couldn't find the event you're looking for. Please try clicking on an event from the events page.");

            // Add a button to the error message that will take the user to the events page
            setTimeout(() => {
              const errorDiv = document.querySelector(".error-message");
              if (errorDiv) {
                const button = document.createElement("button");
                button.innerText = "Go to Events Page";
                button.className = "mt-4 px-6 py-2 bg-[#eb936b] text-white rounded-md hover:bg-[#d87e5a] transition-colors";
                button.onclick = () => navigate('/events');
                errorDiv.appendChild(button);
              }
            }, 100);

            setDataLoading(false);
          }
        } catch (err) {
          console.error("Failed to fetch event data:", err);
          setDataError("Failed to load event data.");

          // Try to use fallback data if available
          if (data && data.images) {
            // Similar logic as above but with fallback data
            // This is simplified for brevity
            setDataError("Using fallback data. Some features may be limited.");
          }
        } finally {
          setDataLoading(false);
        }
      }
    };

    fetchEventData();

    // Cleanup function to prevent state updates on unmounted component
    return () => {
      // This will prevent any state updates after the component unmounts
      console.log("Cleaning up event data fetch effect");
    };
  }, [id]); // Only re-run if the ID changes
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState(null);
  const [isVideoModal, setIsVideoModal] = useState(false);
  const [mainImageLoading, setMainImageLoading] = useState(stateData.image || (eventData && eventData.image) ? true : false);

  // Image preloading strategy for better performance
  const [preloadProgress, setPreloadProgress] = useState({ loaded: 0, total: 0 });
  const [highPriorityLoaded, setHighPriorityLoaded] = useState(false);

  // Enhanced preloading with priority system
  useEffect(() => {
    const currentSubImages = stateData.subImages || (eventData && eventData.subImages) || [];

    if (currentSubImages.length > 0) {
      // Create priority-based image array
      const prioritizedImages = currentSubImages.map((img, index) => ({
        src: img.src,
        priority: index < 3 ? 10 : 1 // First 3 images get high priority
      }));

      // Start batch preloading
      batchPreloadImages(prioritizedImages, {
        highPriorityCount: 3,
        onHighPriorityComplete: () => {
          setHighPriorityLoaded(true);
          console.log('High priority images loaded');
        },
        onProgress: (progress) => {
          setPreloadProgress(progress);
        },
        onAllComplete: () => {
          console.log('All images preloaded');
        }
      });
    }
  }, [stateData.subImages, eventData.subImages]);

  // Add keyboard event listener to close modal with Escape key
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape" && isModalOpen) {
        closeModal();
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    // Cleanup
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [isModalOpen]);

  const phone = (stateData.info?.[0]?.phone || eventData.info?.[0]?.phone || "");

  const openModal = (media, isVideo = false) => {
    setSelectedMedia(media);
    setIsVideoModal(isVideo);
    setIsModalOpen(true);
    // Lock body scroll when modal is open
    document.body.style.overflow = "hidden";
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedMedia(null);
    setIsVideoModal(false);
    // Restore body scroll
    document.body.style.overflow = "auto";
  };

  const [formData, setFormData] = useState({
    name: "",
    phoneNumber: "",
    eventDate: "",
    pax: "",
    venueType: "",
    location: "",
  });

  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name) newErrors.name = "Name is required.";
    if (!formData.phoneNumber) {
      newErrors.phoneNumber = "Phone number is required.";
    } else if (!/^\d{10}$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = "Phone number must be 10 digits.";
    }
    if (!formData.eventDate) newErrors.eventDate = "Event date is required.";
    if (!formData.pax || parseInt(formData.pax, 10) <= 0) {
      newErrors.pax = "Number of guests must be a positive number.";
    }
    if (!formData.venueType) newErrors.venueType = "Venue type is required.";
    if (!formData.location) newErrors.location = "Location is required.";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0; // Return true if no errors
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name === "phoneNumber") {
      // Allow only up to 10 digits
      if (!/^\d{0,10}$/.test(value)) return;
    }

    if (name === "pax") {
      // Allow only positive numbers (no negatives or non-numeric characters)
      if (!/^\d*$/.test(value)) return;
    }
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (validateForm()) {
      try {
        const templateParams = {
          name: formData.name,
          phoneNumber: formData.phoneNumber,
          eventDate: formData.eventDate,
          pax: formData.pax,
          venueType: formData.venueType,
          location: formData.location,
        };

        await emailjs.send(
          "service_pzveien",
          "template_2v1m97k",
          templateParams,
          "9kOEg_nCQd1fWuTGG"
        );

        alert("Form submitted successfully!");
        setFormData({
          name: "",
          phoneNumber: "",
          eventDate: "",
          pax: "",
          venueType: "",
          location: "",
        });
        setErrors({});
      } catch (error) {
        console.error("Failed to send email:", error);
        alert("Failed to send email. Please try again later.");
      }
    }
  };
  return (
    <div className="bg-[#fcf2e9] overflow-hidden  ">
      <Link to="/events" className="absolute top-4 left-4 z-10">
        <img src={Logo} className="w-[10rem] lg:w-30" alt="" />
      </Link>

      {/* Show error message if there's an error fetching data */}
      {dataError && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-70">
          <div className="bg-white p-8 rounded-lg shadow-xl max-w-md text-center error-message">
            <div className="text-red-500 text-5xl mb-4">⚠️</div>
            <h2 className="text-2xl font-bold mb-4">Error</h2>
            <p className="mb-6">{dataError}</p>
            <button
              onClick={() => navigate('/events')}
              className="mt-4 px-6 py-2 bg-[#eb936b] text-white rounded-md hover:bg-[#d87e5a] transition-colors"
            >
              Go to Events Page
            </button>
          </div>
        </div>
      )}

      {/* Show loading overlay when fetching data directly from URL - with reduced opacity */}
      {dataLoading && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-sm text-center">
            <div className="flex flex-col items-center">
              <div className="relative w-16 h-16 mb-3">
                <div className="absolute top-0 left-0 w-full h-full border-4 border-[#c1ab8b] rounded-full animate-ping opacity-75"></div>
                <div className="absolute top-2 left-2 w-12 h-12 border-4 border-[#eb936b] rounded-full animate-spin"></div>
              </div>
              <p className="text-lg font-Anton">Loading event...</p>
            </div>
          </div>
        </div>
      )}

      <section
        className="w-full h-screen flex justify-center items-center relative"
        style={{
          backgroundImage: mainImageLoading ? "none" :
            `url(${stateData.image || (eventData && eventData.image) || ''})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        {/* Main image loader - simplified */}
        {mainImageLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-[#fcf2e9] bg-opacity-70">
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 border-4 border-[#c1ab8b] border-t-[#eb936b] rounded-full animate-spin"></div>
            </div>
          </div>
        )}

        {/* Hidden image to trigger onLoad event */}
        {(stateData.image || (eventData && eventData.image)) ? (
          <img
            src={stateData.image || (eventData && eventData.image)}
            alt="preload"
            className="hidden"
            onLoad={() => setMainImageLoading(false)}
            onError={() => setMainImageLoading(false)}
          />
        ) : (
          // If no image is available, just set loading to false
          <div className="hidden"></div>
        )}

        <h2
          style={{
            transform: "rotate(-8deg)",
            textShadow: "8px 8px 0px rgba(0,0,0,1)",
          }}
          className="font-Anton sm:text-9xl text-6xl text-white font-outline-black-1 capitalize text-center"
        >
          <span className="block">{(stateData.title || (eventData && eventData.title) || "ENGAGEMENT")?.split(" ")[0]}</span>
          <span className="block">{(stateData.title || (eventData && eventData.title) || "CEREMONY")?.split(" ")[1]}</span>
        </h2>

        {/* Border lines at the bottom */}
        <div className="absolute bottom-0 left-0 w-full">
          <div className="h-2 bg-[#eb936b]"></div>
          <div className="h-2 bg-[#70b3a3]"></div>
          <div className="h-2 bg-[#f4cd77]"></div>
          <div className="h-2 bg-[#c2ad8c]"></div>
        </div>
      </section>
      <EventDetailslider />

      {/* Images section */}

      <div className="flex">
        <div className="flex">
          <div className="flex flex-row flex-wrap justify-center ml-2 items-start">
            {/* Display images with optimized lazy loading */}
            {(stateData.subImages || (eventData && eventData.subImages) || [])?.map((subImage, index) => (
              <div
                key={`lazy-image-${index}`}
                className={`border-8 m-2 border-[#c1ab8b] sm:h-[294px] w-[340px] ${
                  index === 1 ? "m-2" : ""
                }`}
              >
                <LazyImage
                  src={subImage.src}
                  alt={`SubImage ${index + 1}`}
                  className="w-full h-full"
                  onClick={() => openModal(subImage)}
                  index={index}
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Content and Form Section */}
      <section className="py-12 bg-gradient-to-b from-[#fcf2e9] to-[#fff8f2]">
        <div className="container mx-auto px-2 sm:px-4 lg:px-6">
          <div className="">
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 lg:gap-8 xl:gap-10">
              {((stateData.info && stateData.info.length > 0) || (eventData.info && eventData.info.length > 0)) ?
                (stateData.info || eventData.info || []).map((item, index) => (
                <div
                  className="lg:col-span-3 bg-white rounded-xl shadow-md p-6 lg:p-8 border border-[#f0e6da]"
                  key={index}
                >
                  <div className="flex flex-col space-y-4 mb-6">
                    <div className="flex items-center gap-3">
                      <span className="flex items-center justify-center w-8 h-8 bg-[#f4cd77] rounded-full text-lg">
                        📍
                      </span>
                      <p className="font-semibold text-gray-700 text-lg">
                        {item.state}
                      </p>
                    </div>

                    <div className="flex items-center gap-3">
                      <span className="flex items-center justify-center w-8 h-8 bg-[#70b3a3] rounded-full text-lg">
                        📞
                      </span>
                      <p className="font-semibold text-gray-700 text-lg">
                        {item.phone}
                      </p>
                    </div>

                    <div className="flex items-center gap-3">
                      <span className="flex items-center justify-center w-8 h-8 bg-[#eb936b] rounded-full text-lg">
                        💰
                      </span>
                      <p className="text-red-500 font-bold text-lg">
                        {item.amount}
                      </p>
                    </div>
                  </div>

                  {/* Heading with decorative elements */}
                  <div className="relative mb-6">
                    <h2 className="text-xl font-bold mb-3">{item.heading}</h2>
                    <div className="h-1 w-20 bg-red-500 rounded-full"></div>
                  </div>

                  {/* Description with styled container */}
                  <div className="mb-12 bg-[#fcf9f5] p-12 rounded-xl border-l-8 border-[#c1ab8b] shadow-lg">
                    <p className="text-gray-600 leading-relaxed text-lg font-medium">
                      {item.description}
                    </p>
                  </div>

                  {/* Facilities Heading with decorative elements */}
                  <div className="relative mb-6">
                    <h2 className="text-xl font-bold mb-3">
                      {item.facalties}
                    </h2>
                    <div className="h-1 w-20 bg-red-500 rounded-full"></div>
                  </div>

                  {/* Facilities List with improved styling */}
                  <ul className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-gray-700">
                    {item.facilitiesitems && item.facilitiesitems.map((facility, facilityIndex) => (
                      <li
                        key={facilityIndex}
                        className="flex items-center gap-2 text-sm bg-[#f9f5ef] p-2 rounded-md"
                      >
                        <span className="text-[#eb936b]">▹</span>
                        <span>{facility}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )) : (
                <div className="lg:col-span-3 bg-white rounded-xl shadow-md p-6 lg:p-8 border border-[#f0e6da] text-center">
                  <p className="text-xl font-Anton text-gray-700">
                    Event information is loading or not available.
                  </p>
                </div>
              )}

              <div className="lg:col-span-2 bg-white rounded-xl shadow-md overflow-hidden border border-[#f0e6da]">
                <div className="bg-gradient-to-r from-[#eb936b] to-[#c1ab8b] py-4 px-6">
                  <h3 className="text-white text-xl font-bold flex items-center justify-center gap-2">
                    <span>📩</span> CONTACT US
                  </h3>
                </div>

                <div className="p-6 lg:p-8">
                  <div className="bg-[#fcf9f5] p-4 rounded-lg mb-6 border-l-4 border-[#f4cd77]">
                    <p className="text-sm text-gray-700">
                      A <strong>best-price guarantee</strong> is offered at the
                      Urban venue. Get more information by calling{" "}
                      <strong className="text-[#eb936b]">{phone}</strong>
                    </p>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-5">
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">
                        Name
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="Your Name"
                        className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#eb936b] focus:border-transparent"
                      />
                      {errors.name && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.name}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-700 font-medium mb-2">
                        Phone Number *
                      </label>
                      <input
                        type="tel"
                        name="phoneNumber"
                        value={formData.phoneNumber}
                        onChange={handleInputChange}
                        placeholder="987XXXXXXX"
                        className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#eb936b] focus:border-transparent"
                      />
                      {errors.phoneNumber && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.phoneNumber}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-700 font-medium mb-2">
                        Event Date
                      </label>
                      <input
                        type="date"
                        name="eventDate"
                        value={formData.eventDate}
                        onChange={handleInputChange}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#eb936b] focus:border-transparent"
                      />
                      {errors.eventDate && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.eventDate}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-700 font-medium mb-2">
                        Number of Guests
                      </label>
                      <input
                        type="number"
                        name="pax"
                        value={formData.pax}
                        onChange={handleInputChange}
                        placeholder="Number of Guests"
                        className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#eb936b] focus:border-transparent"
                      />
                      {errors.pax && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.pax}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-700 font-medium mb-2">
                        Venue Type
                      </label>
                      <select
                        name="venueType"
                        value={formData.venueType}
                        onChange={handleInputChange}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#eb936b] focus:border-transparent"
                      >
                        <option value="">Select Venue Type</option>
                        <option value="Wedding">Wedding</option>
                        <option value="Party">Party</option>
                        <option value="Corporate Event">Corporate Event</option>
                      </select>
                      {errors.venueType && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.venueType}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-700 font-medium mb-2">
                        Location
                      </label>
                      <select
                        name="location"
                        value={formData.location}
                        onChange={handleInputChange}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#eb936b] focus:border-transparent"
                      >
                        <option value="">Select Location</option>
                        <option value="Noida">Noida</option>
                        <option value="Delhi">Delhi</option>
                        <option value="Gurugram">Gurugram</option>
                      </select>
                      {errors.location && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.location}
                        </p>
                      )}
                    </div>

                    <button
                      type="submit"
                      className="w-full bg-gradient-to-r from-[#eb936b] to-[#c1ab8b] text-white py-3 px-4 rounded-lg font-bold text-lg hover:opacity-90 transition-all duration-300 shadow-md"
                    >
                      Submit Inquiry
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Modal with beautiful styling */}
      {isModalOpen && (
        <div
          className="fixed top-0 left-0 w-full h-screen bg-black bg-opacity-70 flex justify-center items-center z-40 backdrop-blur-sm"
          onClick={closeModal}
        >
          <div
            className="bg-[#fcf2e9] p-6 rounded-xl max-w-4xl w-full max-h-screen overflow-auto shadow-2xl transform transition-all duration-300 ease-in-out relative"
            style={{
              boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.5)",
              animation: "modalFadeIn 0.3s ease-out forwards",
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {isVideoModal ? (
              // Enhanced Video content with beautiful styling
              <div className="w-full h-[500px] flex items-center justify-center bg-black relative rounded-lg overflow-hidden">
                {/* Decorative border */}
                <div className="absolute inset-0 border-8 border-[#c1ab8b] pointer-events-none z-20 rounded-lg"></div>

                {/* Video loader with enhanced styling */}
                <div
                  className="absolute inset-0 flex items-center justify-center bg-black z-10"
                  id="video-loader"
                >
                  <div className="flex flex-col items-center">
                    <div className="relative w-24 h-24">
                      <div className="absolute top-0 left-0 w-full h-full border-4 border-[#c1ab8b] rounded-full animate-ping opacity-75"></div>
                      <div className="absolute top-2 left-2 w-20 h-20 border-4 border-[#eb936b] rounded-full animate-spin"></div>
                      <div className="absolute top-4 left-4 w-16 h-16 border-4 border-[#70b3a3] rounded-full animate-pulse"></div>
                      <div className="absolute top-6 left-6 w-12 h-12 border-4 border-[#f4cd77] rounded-full animate-bounce"></div>
                    </div>
                    <p className="text-xl font-Anton mt-4 text-white">
                      Loading your video...
                    </p>
                  </div>
                </div>

                <iframe
                  src={
                    typeof selectedMedia === "string"
                      ? selectedMedia.replace("watch?v=", "embed/") +
                        "?autoplay=1"
                      : ""
                  }
                  title="Event Video"
                  className="w-full h-full z-5"
                  allowFullScreen
                  allow="autoplay; encrypted-media"
                  onLoad={() => {
                    // Hide loader when video is loaded
                    const loader = document.getElementById("video-loader");
                    if (loader) loader.style.display = "none";
                  }}
                ></iframe>
              </div>
            ) : (
              // Image content
              <div className="relative">
                {/* Image loader for swiper */}
                <div
                  className="absolute inset-0 flex items-center justify-center bg-white z-10"
                  id="swiper-loader"
                >
                  <div className="flex flex-col items-center">
                    <div className="w-16 h-16 border-4 border-[#c1ab8b] border-t-[#eb936b] rounded-full animate-spin"></div>
                    <p className="text-gray-700 font-Anton mt-4">
                      Loading gallery...
                    </p>
                  </div>
                </div>

                <Swiper
                  slidesPerView={1}
                  spaceBetween={30}
                  loop={false} // Disable loop mode to prevent warnings
                  navigation={true}
                  pagination={{ clickable: true }}
                  modules={[Navigation, Pagination]}
                  initialSlide={
                    (stateData.subImages || eventData.subImages) && selectedMedia
                      ? (stateData.subImages || eventData.subImages || []).findIndex((item) => item === selectedMedia)
                      : 0
                  }
                  onInit={() => {
                    // Hide loader when swiper is initialized
                    setTimeout(() => {
                      const loader = document.getElementById("swiper-loader");
                      if (loader) loader.style.display = "none";
                    }, 500);
                  }}
                >
                  {(stateData.subImages || eventData.subImages || [])?.map((item, index) => (
                    <SwiperSlide key={index}>
                      <img
                        src={item.src}
                        alt={`Modal Image ${index + 1}`}
                        className="w-full h-[500px] object-contain rounded-lg"
                      />
                    </SwiperSlide>
                  ))}
                </Swiper>
              </div>
            )}

            <button
              onClick={(e) => {
                e.stopPropagation(); // Stop event from bubbling up
                closeModal();
              }}
              className="absolute top-4 right-4 p-2 h-[50px] w-[50px] bg-[#eb936b] text-white font-bold rounded-full shadow-lg hover:bg-[#c1ab8b] transition-all duration-300 transform hover:scale-110 flex items-center justify-center z-50"
              aria-label="Close modal"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={3}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      )}

      <WhichVenueEvent />
      <WhatOurClientSay />
      <SliderEventDetails />
      <Followus />
      <Footer />
      <div className="z-50 fixed">
        <CustomFooter />
      </div>
    </div>
  );
};

export default EventsDetails;
