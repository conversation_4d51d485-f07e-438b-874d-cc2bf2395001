import React, { useState } from "react";
import SparklesText from "../components/ui/sparkles-text";
import Footer from "@/components/Footer/Footer";
import Logo from "../assets/urbanvenuelogo.png";
import { Link } from "react-router-dom";
import emailjs from "emailjs-com";
import CustomFooter from "@/components/Footer/CustomFooter";

const Career = () => {
  const [selectedType, setSelectedType] = useState("");
  const [name, setName] = useState("");
  const [number, setNumber] = useState("");
  const [email, setEmail] = useState("");
  const [gender, setGender] = useState("");
  const [dob, setDob] = useState("");
  const [message, setMessage] = useState("");
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};

    if (!name.trim()) newErrors.name = "Name is required.";
    if (!number || !/^\d{10}$/.test(number) || parseInt(number, 10) <= 0)
      newErrors.number = "Number must be a positive 10-digit value.";
    if (!gender) newErrors.gender = "Gender is required.";
    if (!dob) newErrors.dob = "Date of birth is required.";
    if (!email || !/\S+@\S+\.\S+/.test(email))
      newErrors.email = "Valid email is required.";
    if (!selectedType) newErrors.selectedType = " Type is required.";
    if (!message.trim()) newErrors.message = "Description is required.";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const sendEmail = (e) => {
    console.log({ name, number, email, selectedType, dob, gender, message });

    e.preventDefault();

    if (!validateForm()) return;

    emailjs
      .send(
        "service_pzveien", // Your service ID
        "template_21mc94f", // Your template ID
        {
          name,
          number,
          gender,
          dob,
          email,
          selectedType,
          message,
        },
        "9kOEg_nCQd1fWuTGG" //Public key
      )
      .then(
        () => {
          alert("Email sent successfully!");

          setName("");
          setNumber("");
          setEmail("");
          setGender("");
          setDob("");
          setMessage("");
          setSelectedType("");
          setErrors({});
        },
        (error) => {
          console.error("Email send failed:", error);
          alert("Failed to send email. Please try again.");
        }
      );
  };

  return (
    <div className="w-full bg-[#fcf2e9] overflow-x-hidden flex flex-col justify-center items-center py-4 ">
      <Link to="/" className="absolute top-4 left-4 z-10">
        <img src={Logo} className="w-[10rem] lg:w-30" alt="" />
      </Link>
      <div className="flex flex-col justify-center items-center w-[80%] my-20 ">
        <SparklesText
          className="font-Anton text-7xl text-white font-outline-black-1 my-4 "
          text="JOIN US"
        />
        <p className="font-Archivo tracking-wide mt-10 font-semibold text-[#4d4844]">
          Fill out this form, our team will get back to you as soon as possible!
        </p>
        <form
          onSubmit={sendEmail}
          className="grid grid-cols-2 items-center gap-4 w-full py-4"
        >
          {/* Name */}
          <div>
            <label htmlFor="firstName" className="block mb-1 font-medium">
              Name
            </label>
            <input
              type="text"
              id="firstName"
              className="w-full p-2 border rounded"
              onChange={(e) => setName(e.target.value)}
              value={name}
            />
            {errors.name && (
              <p className="text-red-500 text-sm">{errors.name}</p>
            )}
          </div>

          {/* Phone Number */}
          <div>
            <label htmlFor="phoneNumber" className="block mb-1 font-medium">
              Phone Number
            </label>
            <input
              type="text"
              id="phoneNumber"
              className="w-full p-2 border rounded"
              maxLength="10"
              pattern="\d{10}"
              onChange={(e) => setNumber(e.target.value)}
              value={number}
            />
            {errors.number && (
              <p className="text-red-500 text-sm">{errors.number}</p>
            )}
          </div>

          {/* Gender */}
          <div>
            <label htmlFor="gender" className="block mb-1 font-medium">
              Gender
            </label>
            <select
              id="gender"
              className="w-full p-2 border rounded"
              onChange={(e) => setGender(e.target.value)}
              value={gender}
            >
              <option value="" disabled selected>
                Select Gender
              </option>
              <option value="Male">Male</option>
              <option value="Female">Female</option>
              <option value="Other">Other</option>
              <option value="Prefer not to say">Prefer not to say</option>
            </select>
            {errors.gender && (
              <p className="text-red-500 text-sm">{errors.gender}</p>
            )}
          </div>

          {/* DOB */}
          <div>
            <label htmlFor="dob" className="block mb-1 font-medium">
              DOB
            </label>
            <input
              type="date"
              id="dob"
              className="w-full p-2 border rounded"
              value={dob}
              onChange={(e) => setDob(e.target.value)}
            />
            {errors.dob && <p className="text-red-500 text-sm">{errors.dob}</p>}
          </div>

          {/* Email */}
          <div>
            <label htmlFor="email" className="block mb-1 font-medium">
              Gmail
            </label>
            <input
              type="email"
              id="email"
              className="w-full p-2 border rounded"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{errors.email}</p>
            )}
          </div>

          {/* Occasion */}
          <div>
            <label htmlFor="occasion" className="block mb-1 font-medium">
              Choose The type
            </label>
            <select
              id="occasion"
              className="w-full p-2 border rounded"
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
            >
              <option value="Digital Marketing">Digital Marketing</option>
              <option value="Sales Executive">Sales Executive</option>
              <option value="HR Department">HR Department</option>
              <option value="Content Creator">Content Creator</option>
              <option value="Event Coordinator">Event Coordinator</option>
              <option value="Account Department">Account Department</option>
            </select>
            {errors.selectedType && (
              <p className="text-red-500 text-sm">{errors.selectedType}</p>
            )}
          </div>

          {/* Description */}
          <div className="col-span-2">
            <label htmlFor="message" className="block mb-1 font-medium">
              Description
            </label>
            <textarea
              id="resume"
              rows="8"
              className="w-full p-2 border rounded"
              onChange={(e) => setMessage(e.target.value)}
              value={message}
            />
            {errors.message && (
              <p className="text-red-500 text-sm">{errors.message}</p>
            )}
          </div>

          {/* <div className="col-span-2">
            <label htmlFor="pdf" className="block mb-1 font-medium">
              Upload PDF
            </label>
            <input
              type="file"
              accept="application/pdf"
              className="w-full p-2 border rounded"
              onChange={(e) => setPdfFile(e.target.files[0])}
            />
            {errors.pdfFile && (
              <p className="text-red-500 text-sm">{errors.pdfFile}</p>
            )}
          </div> */}
          <div className="col-span-2 flex justify-center">
            <button
              type="submit"
              className="  relative shadow-custom px-8 py-2 bg-white border border-black font-normal tracking-wide rounded-sm font-Anton transition-all duration-300 hover:scale-105 hover:shadow-xl hover:-translate-y-1 group "
            >
              Send
            </button>
          </div>
        </form>
      </div>
      <Footer />
      <div className="z-50 fixed">
        <CustomFooter />
      </div>
    </div>
  );
};

export default Career;
