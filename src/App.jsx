import React from 'react'
import { Route, Routes } from 'react-router-dom'
import Home from './pages/Home'
import Contact from './pages/Contact'
import { ABOUT, BLOG, CONTACT_US, VENUE_DETAILS, VENUE_DETAILS_WITH_ID, HOME, VENUE, EVENTS, EVENT_DETAILS, EVENT_DETAILS_WITH_ID, CAREER, FOOD_TASTING} from './routes/routes'
import Career from './pages/Career'
import VenueDetails from './pages/VenueDetails'
import NavMenu from './components/NavMenu/NavMenu'
import Venue from './pages/Venues'
import About from './pages/About'
import Blog from './pages/Blog'
import ScrollToTop from './components/ScrollToTop'
import Events from './pages/Events'
import EventsDetails from './pages/EventsDetails'
import FoodTastingModal from './components/FoodOrder/FoodTastingModal'

const App = () => {
  return (
    <>

      <NavMenu />
      <ScrollToTop />
      <Routes>
        <Route path={HOME} element={<Home />} />
        <Route path={CONTACT_US} element={<Contact />} />
        <Route path={CAREER} element={<Career />} />
        <Route path={VENUE_DETAILS} element={<VenueDetails />} />
        <Route path={VENUE_DETAILS_WITH_ID} element={<VenueDetails />} />
        <Route path={VENUE} element={<Venue />} />
        <Route path={ABOUT} element={<About/>}/>
        <Route path={BLOG} element={<Blog/>}/>
        <Route path={EVENTS} element={<Events/>}/>
        <Route path={EVENT_DETAILS} element={<EventsDetails/>}/>
        <Route path={EVENT_DETAILS_WITH_ID} element={<EventsDetails/>}/>
        <Route path={FOOD_TASTING} element={<FoodTastingModal />} />
      </Routes>
    </>
  )
}

export default App
