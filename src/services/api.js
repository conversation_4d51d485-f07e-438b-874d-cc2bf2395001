/**
 * API service for fetching venue and event data
 */

/**
 * Fetches all venue details from the API
 * @returns {Promise<Object>} The venue data
 */
export const fetchAllVenues = async () => {
  try {
    const response = await fetch(
      "http://localhost:9000/api/allVenues/get-all-venue-details"
    );

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching venue data:", error);
    throw error;
  }
};

/**
 * Fetches all event details from the API
 * @returns {Promise<Object>} The event data
 */
export const fetchAllEvents = async () => {
  try {
    const response = await fetch(
      "http://localhost:9000/api/allEvents/get-all-event-details"
    );

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching event data:", error);
    throw error;
  }
};

/**
 * Processes the API response into a format compatible with the existing app structure
 * @param {Object} apiData - The raw API response data
 * @returns {Object} Processed data in the format expected by the app
 */
export const processVenueData = (apiData) => {
  if (!apiData || !apiData.success || !apiData.data) {
    return {
      cities: [],
      venueTypes: [],
      images: {},
    };
  }

  // Extract unique cities
  const cities = apiData.data.map((venue) => ({
    name: venue.locationName.trim().toUpperCase(),
    selectedBorder: false,
  }));

  // Extract unique venue types
  const venueTypes = [];
  apiData.data.forEach((venue) => {
    venue.venueDetails.forEach((detail) => {
      if (
        !venueTypes.some(
          (type) => type.name === detail.categoryName.toUpperCase()
        )
      ) {
        venueTypes.push({
          name: detail.categoryName.toUpperCase(),
          selectedBackground: false,
        });
      }
    });
  });

  // Process images and videos
  const images = {};

  apiData.data.forEach((venue) => {
    const cityName = venue.locationName.trim().toUpperCase();
    images[cityName] = {};

    venue.venueDetails.forEach((detail) => {
      const venueType = detail.categoryName.toUpperCase();

      if (!images[cityName][venueType]) {
        images[cityName][venueType] = [];
      }

      detail.venueDetails.forEach((venueDetail) => {
        // Process each venue
        const venueImages = venueDetail.images || [];
        const venueVideos = venueDetail.videos || [];

        // Determine if this is a video or image venue
        const hasVideo = venueVideos && venueVideos.length > 0;
        const hasImages = venueImages && venueImages.length > 0;

        // Create an entry for this venue
        images[cityName][venueType].push({
          src: hasImages ? venueImages[0] : "",
          alt: detail.categoryName,
          subImages: venueImages.map((img) => ({ src: img })),
          videos: venueVideos,
          hasVideo: hasVideo,
          hasImages: hasImages,
          info: [
            {
              state: venueDetail.locationName || "",
              phone: venueDetail.phoneNumber || "",
              amount: venueDetail.price ? `${venueDetail.price}/-` : "",
              heading: "DISCOVER MORE",
              description: venueDetail.description || "",
              facalties: "AMENITIES",
              facilitiesitems: venueDetail.amenities
                ? venueDetail.amenities.split(",").map((item) => item.trim())
                : [],
            },
          ],
        });
      });
    });
  });

  return {
    cities,
    venueTypes,
    images,
  };
};

/**
 * Processes the API response for events into a format compatible with the existing app structure
 * @param {Object} apiData - The raw API response data
 * @returns {Object} Processed data in the format expected by the app
 */
export const processEventData = (apiData) => {
  if (!apiData || !apiData.success || !apiData.data) {
    return {
      cities: [],
      eventTypes: [],
      images: {},
    };
  }

  // Extract unique cities
  const cities = apiData.data.map((event) => ({
    name: event.locationName.trim().toUpperCase(),
    selectedBorder: false,
  }));

  // Extract unique event types
  const eventTypes = [];
  apiData.data.forEach((event) => {
    event.eventDetails.forEach((detail) => {
      if (
        !eventTypes.some(
          (type) => type.name === detail.categoryName.toUpperCase()
        )
      ) {
        eventTypes.push({
          name: detail.categoryName.toUpperCase(),
          selectedBackground: false,
        });
      }
    });
  });

  // Process images and videos
  const images = {};

  apiData.data.forEach((event) => {
    const cityName = event.locationName.trim().toUpperCase();
    images[cityName] = {};

    event.eventDetails.forEach((detail) => {
      const eventType = detail.categoryName.toUpperCase();

      if (!images[cityName][eventType]) {
        images[cityName][eventType] = [];
      }

      detail.eventDetails.forEach((eventDetail) => {
        // Process each event
        const eventImages = eventDetail.images || [];
        const eventVideos = eventDetail.videos || [];

        // Determine if this is a video or image event
        const hasVideo = eventVideos && eventVideos.length > 0;
        const hasImages = eventImages && eventImages.length > 0;

        // Create an entry for this event
        images[cityName][eventType].push({
          src: hasImages ? eventImages[0] : "",
          alt: detail.categoryName,
          subImages: eventImages.map((img) => ({ src: img })),
          videos: eventVideos,
          hasVideo: hasVideo,
          hasImages: hasImages,
          info: [
            {
              state: eventDetail.locationName || "",
              phone: eventDetail.phoneNumber || "",
              amount: eventDetail.price ? `${eventDetail.price}/-` : "",
              heading: "DISCOVER MORE",
              description: eventDetail.description || "",
              facalties: "AMENITIES",
              facilitiesitems: eventDetail.amenities
                ? eventDetail.amenities.split(",").map((item) => item.trim())
                : [],
            },
          ],
        });
      });
    });
  });

  return {
    cities,
    eventTypes,
    images,
  };
};
