import React, { useState } from "react";
import { Send, Close } from "@mui/icons-material";
import { Favorite } from "@mui/icons-material";

const NotificationBadge = ({ notificationCount = 10 }) => {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    mobileNumber: "",
    eventDate: "",
    location: "",
  });

  const [errors, setErrors] = useState({});

  const validateForm = () => {
    let newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required.";
    }

    if (!formData.mobileNumber.trim()) {
      newErrors.mobileNumber = "Mobile number is required.";
    } else if (!/^\d{10}$/.test(formData.mobileNumber)) {
      newErrors.mobileNumber = "Enter a valid 10-digit mobile number.";
    }

    if (!formData.eventDate) {
      newErrors.eventDate = "Event date is required.";
    } else {
      const selectedDate = new Date(formData.eventDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (selectedDate < today) {
        newErrors.eventDate = "Event date cannot be in the past.";
      }
    }

    if (!formData.location.trim()) {
      newErrors.location = "Location is required.";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  // const handleSubmit = (e) => {
  //   e.preventDefault();
  //   if (validateForm()) {
  //     alert("Form submitted successfully!");
  //     setFormData({ name: "", mobileNumber: "", eventDate: "", location: "" });
  //     setTimeout(() => setIsFormOpen(false), 500);
  //   }
  // };


  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    const payload = {
      name: formData.name,
      email: "", // Leave blank or collect if needed
      phone: formData.mobileNumber,
      additional_information: "", // Add any additional info if needed
      occassion: "", // Add occassion if needed
      location: formData.location,
      enquiry_date: formData.eventDate,
    };

    try {
      const response = await fetch('https://portal.deegeetech.com/leadsapi/data/42c83cb51cbfc3a14de82d63eb471dec', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': 'spcrm=b7f0f28c9f739635ca4e6b28239c38ce9d1b36d'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error("Failed to submit form");
      }

      alert("Form submitted successfully!");

      setFormData({
        name: "",
        mobileNumber: "",
        eventDate: "",
        location: "",
      });

      setTimeout(() => setIsFormOpen(false), 500);
    } catch (error) {
      alert("Something went wrong. Please try again later.");
      console.error(error);
    }
  };


  return (
    <div className="fixed bottom-6 right-6 flex flex-col items-end z-50">
      {/* Form Window */}
      {isFormOpen && (
        <div className="w-80 bg-white/80 backdrop-blur-lg shadow-2xl rounded-xl overflow-hidden border border-gray-300 transition-all transform scale-95 animate-fadeIn p-4">
          {/* Form Header */}
          <div className="bg-gradient-to-r bg-[#EF4444] text-white px-4 py-3 flex justify-between items-center">
            <span className=" sm:text-xl text-lg">
              Event Enquiry
            </span>
            <button
              onClick={() => setIsFormOpen(false)}
              className="text-2xl hover:scale-110 transition"
            >
              <Close fontSize="large" />
            </button>
          </div>

          {/* Form Fields */}
          <form onSubmit={handleSubmit} className="p-4 flex flex-col gap-3">
            <div>
              <input
                type="text"
                name="name"
                placeholder="Your Name"
                value={formData.name}
                onChange={handleChange}
                className="p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition w-full"
                required
              />
              {errors.name && (
                <p className="text-red-500 text-sm">{errors.name}</p>
              )}
            </div>

            <div>
              <input
                type="tel"
                name="mobileNumber"
                placeholder="Mobile Number"
                value={formData.mobileNumber}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, ""); // Remove non-numeric characters
                  if (value.length <= 10) {
                    setFormData({ ...formData, mobileNumber: value });
                  }
                }}
                className="p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition w-full"
                required
              />
              {errors.mobileNumber && (
                <p className="text-red-500 text-sm">{errors.mobileNumber}</p>
              )}
            </div>

            <div>
              <input
                type="date"
                name="eventDate"
                value={formData.eventDate}
                onChange={handleChange}
                className="p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition w-full"
                required
              />
              {errors.eventDate && (
                <p className="text-red-500 text-sm">{errors.eventDate}</p>
              )}
            </div>

            <div>
              <input
                type="text"
                name="location"
                placeholder="Event Location"
                value={formData.location}
                onChange={handleChange}
                className="p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition w-full"
                required
              />
              {errors.location && (
                <p className="text-red-500 text-sm">{errors.location}</p>
              )}
            </div>

            <button
              type="submit"
              className="bg-[#EF4444] text-white p-3 rounded-lg hover:bg-blue-700 transition flex items-center justify-center gap-2  sm:text-xl text-lg">
              <Send />
              Submit
            </button>
          </form>
        </div>
      )}

      {/* Profile Image & Notification */}
      <div
        className="relative w-20 h-20 cursor-pointer group"
        onClick={() => setIsFormOpen(!isFormOpen)}
      >
        <div className="w-[60px] h-[60px] bg-red-500 rounded-full flex items-center justify-center border-4 border-white shadow-xl overflow-hidden transition-transform group-hover:scale-105">
          <Favorite className="w-[60px] h-[60px] object-cover rounded-full" />
        </div>

        {/* Notification Badge */}
        {notificationCount > 0 && (
          <div className="absolute -top-2 -right-0 w-7 h-7 bg-red-600 text-white text-sm font-bold flex items-center justify-center rounded-full border-2 border-white shadow-md animate-bounce">
            {notificationCount}
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationBadge;
