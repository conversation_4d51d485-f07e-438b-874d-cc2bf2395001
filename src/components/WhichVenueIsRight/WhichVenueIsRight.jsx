import React from "react";
import svg from "../../assets/illu-rainbow-wave.svg.svg";
import spin1 from "../../assets/mehndiMarqee.jpg";
import spin2 from "../../assets/discover4.png";
import spin3 from "../../assets/ringdiscover3.png";
import spin4 from "../../assets/HomeLatest.png";
import spin5 from "../../assets/birthday.jpeg";
import spin6 from "../../assets/haldi.png";

import { Link } from "react-router-dom";

const WhichVenueIsRight = () => {
  const sparkle1Stars = [...Array(10)].map((_, index) => {
    const size = Math.random() * 100 + 2;
    const top = Math.random() * 100;
    const left = Math.random() * 100;
    const opacity = Math.random() * 0.4 + 0.2;
    const animationDelay = Math.random() * 2;

    return (
      <span
        key={`sparkle1-${index}`}
        className="absolute bg-white rounded-full animate-blink sparkle1"
        style={{
          width: `${size}px`,
          height: `${size}px`,
          top: `${top}%`,
          left: `${left}%`,
          opacity: opacity,
          animationDelay: `${animationDelay}s`,
          background: "#ffc107",
        }}
      ></span>
    );
  });

  const sparkleStars = [...Array(10)].map((_, index) => {
    const size = Math.random() * 100 + 2;
    const top = Math.random() * 100;
    const left = Math.random() * 100;
    const opacity = Math.random() * 0.4 + 0.2;
    const animationDelay = Math.random() * 2;

    return (
      <span
        key={`sparkle-${index}`}
        className="absolute bg-white rounded-full animate-blink sparkle"
        style={{
          width: `${size}px`,
          height: `${size}px`,
          top: `${top}%`,
          left: `${left}%`,
          opacity: opacity,
          animationDelay: `${animationDelay}s`,
          background: "#eb9261",
        }}
      ></span>
    );
  });

  const images = [
    {
      name: "Mehndi Function",
      eventType: "MEHNDI FUNCTION",
      image: spin1, // Ensure `spin1` is imported or defined
    },
    // {
    //   name: "ECO FRIENDLY PARTIE'S",
    //   image: spin2, // Ensure `spin2` is imported or defined
    // },
    {
      name: "Ring Ceremony",
      eventType: "RING CEREMONY",
      image: spin3, // Ensure `spin3` is imported or defined
    },
    {
      name: "Office Party",
      eventType: "OFFICE PARTY",
      image: spin4, // Ensure `spin4` is imported or defined
    },
    {
      name: "Birthday Party",
      eventType: "BIRTHDAY PARTY",
      image: spin5, // Ensure `spin5` is imported or defined
    },
    {
      name: "Haldi Function ",
      eventType: "HALDI FUNCTION",
      image: spin6, // Ensure `spin6` is imported or defined
    },
  ];

  return (
    <div className="relative min-h-screen bg-[#fcf2e9]">
      {/* SVG as background */}
      <div
        className="absolute inset-0 bg-cover bg-center pointer-events-none z-0"
        style={{ backgroundImage: `url(${svg})` }}
      />

      {/* Sparkle effects */}
      <div className="absolute inset-0 pointer-events-none z-0">
        {sparkleStars}
        {sparkle1Stars}
      </div>

      {/* Content in front */}
      <div className="container mx-auto flex flex-col items-center justify-center min-h-screen px-4 py-8 relative z-10">
        <h2
          style={{
            transform: "rotateX(-28deg) rotateY(16deg)",
            // textShadow: "8px 8px 0px rgba(0,0,0,1)",
          }}
          className="font-Anton font-outline-black-1 sm:text-8xl text-6xl text-white my-20 text-center text-special"
        >
          <span className="uppercase">Which event you</span>
          <br />
          <span className="uppercase">looking for?</span>
        </h2>

        {/* Grid for images */}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-2">
          {images.map((image, index) => (
            <Link key={index} to="/events"  state={{ name: image.eventType }}>
              <div
                key={index}
                className="bg-gray-100 rounded-2xl overflow-hidden shadow-md  w-[90%] sm:w-[300px] md:w-[380px] h-[250px] sm:h-[300px] md:h-[400px] mx-auto flex flex-col items-center justify-center relative border-8 border-[#c1ab8b] hover:scale-90 transition-transform duration-300"
                // style={{ width: "450px", height: "450px" }}
              >
                <img
                  src={image.image}
                  alt={image.name}
                  className="w-full object-cover"
                  style={{ height: "100%" }}
                />
                {/* Text overlay */}
                <div className="absolute bottom-0 w-full bg-black bg-opacity-50 text-white text-center py-2">
                  <p className="font-Anton">{image.name}</p>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Text below images */}
        <div className="flex flex-col justify-center items-center my-4">
          <p className="text-center font-Archivo text-[16.41px] w-[86%] font-normal leading-[30.6px] tracking-[1px]">
            Browse Hundreds Of Wedding Venues, Engagement Parties, And Other
            Party Venues
          </p>
        </div>

        {/* Button */}
        <Link to="/events">
          <button className="relative shadow-custom px-8 py-2 bg-white border border-black font-normal tracking-wide rounded-sm font-Anton transition-all duration-300 hover:scale-105 hover:shadow-xl hover:-translate-y-1 group ">
            READ MORE
          </button>
        </Link>
      </div>
    </div>
  );
};

export default WhichVenueIsRight;
