import React, { useEffect, useState } from "react";
import callimg from "../../assets/call.gif";
const CustomBanner = () => {
  const [bgColor, setBgColor] = useState("bg-[#eb936b]");

  useEffect(() => {
    let timer;

    const handleScroll = () => {
      clearTimeout(timer);
      timer = setTimeout(() => {
        const scrollTop = window.scrollY;

        // Change the background color based on scroll position
        if (scrollTop < 1000) {
          setBgColor("bg-[#70b3a3]");
        } else if (scrollTop < 6000) {
          setBgColor("bg-[#f4cd77]");
        } else if (scrollTop < 12000) {
          setBgColor("bg-[#c2ad8c]");
        } else {
          setBgColor("bg-[#c2ad8c]");
        }
      }, 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
      clearTimeout(timer);
    };
  }, []);

  return (
    <a href="tel:+91 9871371364">
      <div
        className={`fixed top-1/2 right-0 h-16 w-16 ${bgColor} flex items-center justify-center transform -translate-y-1/2 rounded-full`}
      >
        <button className="flex flex-col items-center text-white font-bold text-lg gap-3">
          <img
            src={callimg}
            className="rotate-90 w-10 h-10 md:w-12 md:h-12 rounded-full object-cover"
          />
        </button>
      </div>
    </a>
  );
};

export default CustomBanner;
