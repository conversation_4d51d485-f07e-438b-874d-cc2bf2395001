import React, { useState } from "react";

const FoodTastingModal = ({ onClose }) => {
  const [fullName, setFullName] = useState("");
  const [mobileNumber, setMobileNumber] = useState("");
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    let newErrors = {};

    if (!fullName.trim()) {
      newErrors.fullName = "Full name is required";
    }

    if (!/^\d{10}$/.test(mobileNumber)) {
      newErrors.mobileNumber = "Mobile number must be exactly 10 digits";
    }

    setErrors(newErrors);

    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      console.log("Schedule", { fullName, mobileNumber });

      setFullName("");
      setMobileNumber("");
      setErrors({});
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50  backdrop-blur-lg">
      <div className="bg-white rounded-lg p-8 w-[450px] shadow-lg relative">
        {/* Close Button */}
        <button
          className="absolute top-4 right-6 text-gray-500 hover:text-black"
          onClick={onClose}
        >
          ✖
        </button>

        {/* Title */}
        <h2 className="font-Anton text-5xl mb-4">Free Food Tasting</h2>

        {/* Features */}
        <div className="space-y-2 text-gray-600 mb-6">
          <div className="flex items-center gap-4 text-gray-700 text-sm">
            <span className="">⭐</span> Verified Listings
          </div>
          <div className="flex items-center gap-4 text-gray-700 text-sm">
            <span className="">👌</span> Hassle Free
          </div>
          <div className="flex items-center gap-4 text-gray-700 text-sm">
            <span className="">💰</span> Best Price Guarantee
          </div>
        </div>

        {/* Form */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-600">
              FULL NAME
            </label>
            <input
              type="text"
              placeholder="Enter your full name"
              className="w-full px-4 py-2 border border-black focus:outline-none"
              value={fullName} 
              onChange={(e) => setFullName(e.target.value)}
            />
            {errors.fullName && (
              <p className="text-red-500 text-sm mt-1">{errors.fullName}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-600">
              MOBILE NUMBER
            </label>
            <input
              type="text"
              placeholder="Enter Mobile No"
              className="w-full px-4 py-2 border border-black focus:outline-none"
              value={mobileNumber} 
              onChange={(e) => {
                const inputValue = e.target.value;
                if (/^\d{0,10}$/.test(inputValue)) {
                  setMobileNumber(inputValue);
                }
              }}
            />
            {errors.mobileNumber && (
              <p className="text-red-500 text-sm mt-1">{errors.mobileNumber}</p>
            )}
          </div>
        </div>

        {/* Terms & Conditions */}
        <p className="text-xs text-gray-500 mt-4 text-center">
          By Continuing I agree with the Terms of Use & Privacy Policy
        </p>

        {/* Schedule Button */}
        <button
         onClick={handleSubmit}
          className="relative w-full shadow-custom px-4 py-2 md:px-6 md:py-3 lg:px-8 lg:py-2 bg-white border border-black font-bold rounded-sm mt-4 z-20 transition-all duration-300 hover:scale-105 hover:shadow-xl hover:-translate-y-1 group"
        >
          SCHEDULE
        </button>
      </div>
    </div>
  );
};

export default FoodTastingModal;
