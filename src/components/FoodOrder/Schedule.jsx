import React, { useState, useEffect } from "react";
import svg from "../../assets/illu-rainbow-wave.svg.svg";
import FoodTastingModal from "./FoodTastingModal";
import img1 from "../../assets/food1.jpg";
import img2 from "../../assets/food2.jpg";
import img3 from "../../assets/food3.jpg";
import img4 from "../../assets/dish1.jpg";
import img5 from "../../assets/dish2.jpg";

const Schedule = () => {
  const images = [img1, img2, img3, img4, img5];
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const sparkleStars = [...Array(10)].map((_, index) => {
    const size = Math.random() * 100 + 2;
    const top = Math.random() * 100;
    const left = Math.random() * 100;
    const opacity = Math.random() * 0.4 + 0.2;
    const animationDelay = Math.random() * 2;

    return (
      <span
        key={`sparkle-${index}`}
        className=" bg-white rounded-full animate-blink sparkle"
        style={{
          width: `${size}px`,
          height: `${size}px`,
          top: `${top}%`,
          left: `${left}%`,
          opacity: opacity,
          animationDelay: `${animationDelay}s`,
          background: "#eb9261",
        }}
      ></span>
    );
  });
  const sparkle1Stars = [...Array(10)].map((_, index) => {
    const size = Math.random() * 100 + 2;
    const top = Math.random() * 100;
    const left = Math.random() * 100;
    const opacity = Math.random() * 0.4 + 0.2;
    const animationDelay = Math.random() * 2;

    return (
      <span
        key={`sparkle1-${index}`}
        className=" bg-white rounded-full animate-blink sparkle1"
        style={{
          width: `${size}px`,
          height: `${size}px`,
          top: `${top}%`,
          left: `${left}%`,
          opacity: opacity,
          animationDelay: `${animationDelay}s`,
          background: "#ffc107",
        }}
      ></span>
    );
  });

  const [currentImage, setCurrentImage] = useState(0);

  // Change image based on interval
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImage((prev) => (prev + 1) % images.length);
    }, 3000); // Change image every 3 seconds

    return () => clearInterval(interval); // Cleanup the interval
  }, [images.length]);

  return (
    <div className="bg-[#fcf2e9] h-screen overflow-hidden relative flex flex-col md:flex-row items-center justify-center md:justify-around px-6 sm:px-10 md:px-20 lg:px-40">
      {/* Background SVG Image */}
      <img
        src={svg}
        alt="Background Design"
        className="absolute inset-0 w-full h-full object-cover opacity-90 pointer-events-none"
      />

      {/* Sparkles */}
      <div className="absolute inset-0 pointer-events-none z-0">
        {sparkleStars}
        {sparkle1Stars}
      </div>

      {/* <h2
        className="font-Anton  text-6xl lg:text-8xl text-white z-10 "
        style={{
          WebkitTextStroke: "2px black",
          textShadow: "10px 10px 1px rgba(0,0,0,1)",
        }}
      >
        <span>Get FREE</span> <br />
        <span> Food Tasting</span>
      </h2> */}
      <h2
        className="font-Anton text-6xl lg:text-8xl text-white z-10"
        style={{
          WebkitTextStroke: "2px black",
          textShadow: "4px 4px 1px rgba(0,0,0,1)", // Lighter shadow for small screens
        }}
      >
        <span className="lg:hidden">Get FREE</span> <br />
        <span className="lg:hidden"> Food Tasting</span>
        <span
          className="hidden lg:inline"
          style={{
            textShadow: "10px 10px 1px rgba(0,0,0,1)", // Original shadow for large screens
          }}
        >
          Get FREE <br /> Food Tasting
        </span>
      </h2>

      <img
        src={images[currentImage]}
        alt="cupcake"
        className="w-32 lg:mt-0 mt-[29px] mr-0 md:mr-[10px] sm:w-40 md:w-48 lg:w-56 xl:w-[400px] h-auto object-cover z-10 p-4 sm:p-6 md:p-8 lg:p-10 rounded-full shadow-lg border-4 border-white transition-transform duration-300  hover:scale-110 hover:shadow-2xl"
      />

      <button
        onClick={openModal}
        className="mt-6 md:mt-0 relative shadow-custom px-6 sm:px-8 lg:px-14 py-4 bg-white border border-black font-bold rounded-md z-10 transition-all duration-300 hover:scale-105 hover:shadow-xl hover:-translate-y-1"
      >
        SCHEDULE
      </button>

      {/* Modal */}
      {isModalOpen && <FoodTastingModal onClose={closeModal} />}
    </div>
  );
};

export default Schedule;
