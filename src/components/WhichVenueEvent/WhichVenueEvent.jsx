import React from "react";
import svg from "../../assets/illu-rainbow-wave.svg.svg";
import spin1 from "../../assets/noida-farm/1505/1505-9.jpg";
import spin2 from "../../assets/Gurugram-farm/1212/1212-13.jpeg";
import spin3 from "../../assets/Delhi_farm/9022/9022-11.jpg";
import { Link } from "react-router-dom";
const WhichVenueEvent = () => {
  const venues = [
    { image: spin1, cityName: "Noida", uniqueName: "NOIDA" },
    { image: spin2, cityName: "GURUGRAM", uniqueName: "GURUGRAM" },
    { image: spin3, cityName: "New Delhi", uniqueName: "NEW DELHI" },
  ];

  return (
    <div className="relative min-h-screen bg-[#fcf2e9]">
      {/* SVG as background */}
      <div
        className="absolute inset-0 bg-cover bg-center pointer-events-none z-0"
        style={{ backgroundImage: `url(${svg})` }}
      />

      {/* Content in front */}
      <div className="container mx-auto flex flex-col items-center justify-center min-h-screen px-4 py-8 relative z-10">
        <h2
          style={{
            transform: "rotateX(-28deg) rotateY(16deg)",
            // textShadow: "8px 8px 0px rgba(0,0,0,1)",
          }}
          className="font-Anton font-outline-black-1 lg:text-[4rem] xl:text-8xl sm:text-7xl text-[3rem] sm:mb-[5rem] mb-[6rem] text-white my-8 lg:w-[56%] md:w-[75%]  w-[300px] text-special "
        >
          FOR WHICH LOCATION YOU ARE LOOKING FOR ?
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-2">
          {venues.map((venue, index) => (
            <Link
              to="/venue"
              key={index}
              className="group bg-gray-100 rounded-3xl overflow-hidden shadow-lg max-h-[50vh] flex flex-col items-center justify-center relative border-8 border-[#c1ab8b] transform transition-all duration-700 ease-in-out hover:-translate-y-4 hover:scale-[1.04] hover:shadow-2xl"
              state={{ uniqueName: venue.uniqueName }}
            >
              {/* Image with zoom-in effect on hover */}
              <div className="w-full h-full overflow-hidden">
                <img
                  src={venue.image}
                  alt={`Image ${index}`}
                  className="w-full h-full object-cover transition-transform duration-700 ease-in-out group-hover:scale-110"
                />
              </div>

              {/* Smooth text overlay */}
              <div className="absolute bottom-0 w-full bg-black bg-opacity-60 text-white text-center py-2 transition-all duration-500 ease-in-out group-hover:py-4">
                <p className="font-Anton text-sm sm:text-base md:text-lg transition-opacity duration-500 ease-in-out group-hover:opacity-100">
                  {`Farmhouse In - ${venue.cityName}`}
                </p>
              </div>
            </Link>
          ))}
        </div>

        <div className="flex flex-col justify-center items-center my-4">
          <p className="text-center font-Archivo text-[16.41px] w-[86%] font-normal leading-[30.6px] tracking-[1px]">
            {/* EXPLORE DIFFERENT VENUES TO FIND THE ONE THAT SUITS YOU BEST. */}
            Looking for the Perfect Venue? Explore Wedding, Party & Event
            Spaces!
          </p>
        </div>
        {/* Button */}
        <Link
          to="/venue"
          className="relative shadow-custom px-4 py-2 md:px-6 md:py-3 lg:px-8 lg:py-2 bg-white border border-black font-bold rounded-sm mt-4 z-20 transition-all duration-300 hover:scale-105 hover:shadow-xl hover:-translate-y-1 group"
        >
          READ MORE
        </Link>
      </div>
    </div>
  );
};

export default WhichVenueEvent;
