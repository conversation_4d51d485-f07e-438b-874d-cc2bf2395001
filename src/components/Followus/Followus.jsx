import React from "react";
import svg from "../../assets/illu-rainbow-wave.svg.svg";
import follows1 from "../../assets/follows1.png"
import follows2 from "../../assets/follows2.png"
import follows3 from "../../assets/follows3.png"
import follows4 from "../../assets/follows4.png"
import follows5 from "../../assets/follows5.png"
import follows6 from "../../assets/follows6.png"
import follows7 from "../../assets/follows7.png"
import follows8 from "../../assets/follows8.png"
import follows9 from "../../assets/follows9.png"
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/autoplay";
import { Autoplay, Navigation } from "swiper/modules";
import { Link } from "react-router-dom";

const Followus = () => {
  const images = [
     follows1,
     follows2,
     follows3,
     follows4,
     follows5,    
     follows6,
     follows7,
     follows8,
     follows9,
  ];

  const getMarginClass = (index) => {
    if (index % 0 === 0) return "mt-12";
    if (index % 2 === 1) return "mt-12";

    return "mt-4";
  };

  return (
    <div className="relative min-h-screen bg-[#fcf2e9]">
      {/* SVG as background */}
      <div
        className="absolute inset-0 bg-cover bg-center pointer-events-none z-0"
        style={{ backgroundImage: `url(${svg})` }}
      />

      {/* Content in front */}
      <div className="container mx-auto flex flex-col items-center justify-center min-h-screen px-4 py-8 relative z-10">
        <p className="font-Anton font-outline-black-1 text-4xl bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 bg-clip-text text-transparent  tracking-wider ">
          @theurbanvenue
        </p>
        <h2 className="font-Anton font-outline-black-1 sm:text-8xl text-4xl text-[#302d2b] my-8">
          YOU LIKE US FOLLOW US?
        </h2>

        {/* <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-2"> */}
        <Swiper
          modules={[Autoplay, Navigation]}
          autoplay={{ delay: 1000, disableOnInteraction: false }}
          navigation
          loop={true}
          spaceBetween={30}
          slidesPerView={1}
          speed={500}
          breakpoints={{
            768: { slidesPerView: 3 },
          }}
          className="w-full"
        >
          {images.map((image, index) => (
            <SwiperSlide
              key={index}
              className={`bg-gray-100 rounded-2xl overflow-hidden shadow-md max-h-[50vh] flex flex-col items-center justify-center relative ${getMarginClass(
                index
              )}`}
            >
              <img
                src={image}
                alt={`Image ${index}`}
                className="w-full object-cover"
                style={{ height: "100%", minWidth: "350px" }}
              />
            </SwiperSlide>
          ))}
        </Swiper>
        {/* </div> */}

        {/* Instagram Button */}
        <Link to="https://www.instagram.com/theurbanvenue/" target="_blank">
          <button className="font-Anton shadow-custom p-2 bg-white border border-black font-bold rounded-full my-4 tracking-[1px] transition-all duration-300 hover:scale-105 hover:shadow-xl hover:-translate-y-1 group">
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/a/a5/Instagram_icon.png"
              alt="Instagram"
              className="w-6 h-6  "
            />
          </button>
        </Link>
      </div>
    </div>
  );
};

export default Followus;
