import React, { useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/autoplay";
import { Autoplay, Navigation } from "swiper/modules";
import slidimg1 from "../../assets/about1.png";
import slidimg2 from "../../assets/about2.png";
import slidimg3 from "../../assets/about3.png";
import slidimg4 from "../../assets/about4.png";
import slidimg5 from "../../assets/about5.png";
import slidimg6 from "../../assets/about6.png";

const SliderThree = () => {
  const prevRef = useRef(null);
  const nextRef = useRef(null);
  const images = [slidimg1, slidimg2, slidimg3, slidimg4, slidimg5, slidimg6];

  return (
    <div className="bg-[#fcf2e9] overflow-hidden">
      <div className="sm:py-[100px] py-[100px] flex items-center justify-center relative px-3">
        <Swiper
        spaceBetween={100}
        slidesPerView={3.7}
        modules={[Navigation, Autoplay]}
        autoplay={{ delay: 3000, disableOnInteraction: false }}
        navigation={{
          prevEl: prevRef.current,
          nextEl: nextRef.current,
        }}
        onBeforeInit={(swiper) => {
          swiper.params.navigation.prevEl = prevRef.current;
          swiper.params.navigation.nextEl = nextRef.current;
        }}
        breakpoints={{
          // Define breakpoints and corresponding settings
          375: {
            slidesPerView: 2, // Small screens (e.g., mobile)
          },
          1024: {
            slidesPerView: 3.7, // Larger screens
          },
        }}

        >
          {images.map((image, index) => (
            <SwiperSlide key={index} className="" > 
              <img
                src={image}
                alt={`Slide ${index + 1}`}
                className="w-full object-cover rounded-2xl "
                style={{ height: "80%", minWidth: "190px" }}
              />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
};

export default SliderThree;




