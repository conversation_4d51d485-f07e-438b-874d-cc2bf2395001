import { useState, useEffect } from "react";
import svg from "../../assets/illu-rainbow-wave.svg.svg";
import { Link, useLocation } from "react-router-dom";
import data from "../../assets/VenueData.js"; // Keep for fallback
import { fetchAllVenues, processVenueData } from "../../services/api.js";

// Function to generate a unique ID for each venue
const generateUniqueId = (venue, index, city, venueType) => {
  // Create a slug from venue properties
  const citySlug = city ? city.toLowerCase().replace(/\s+/g, '-') : '';
  const typeSlug = venueType ? venueType.toLowerCase().replace(/\s+/g, '-') : '';
  const titleSlug = venue.alt ? venue.alt.toLowerCase().replace(/\s+/g, '-') : '';

  // Combine multiple properties to ensure uniqueness
  const uniqueId = `${citySlug}-${typeSlug}-${titleSlug}-${index}`;

  // Make URL-friendly by removing special characters
  return uniqueId.replace(/[^a-z0-9-]/g, '');
};

const PreferredVenue = () => {
  const location = useLocation();
  const { uniqueName } = location.state || {};

  // Initialize data states
  const [venueData, setVenueData] = useState(data);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [imageLoading, setImageLoading] = useState({});

  // Get saved media type from localStorage or default to IMAGES
  const getSavedMediaType = () => {
    const savedMediaType = localStorage.getItem("mediaType");
    return savedMediaType || "IMAGES";
  };

  // State for media type with localStorage persistence
  const [mediaType, setMediaType] = useState(getSavedMediaType);

  // Get first city as default if available, otherwise use saved value or NEW DELHI
  const getDefaultCity = () => {
    const savedCity = localStorage.getItem("selectedCity");
    if (savedCity) {
      return savedCity;
    }
    if (venueData && venueData.cities && venueData.cities.length > 0) {
      return venueData.cities[0].name;
    }
    return "NEW DELHI";
  };

  // State for selected city with localStorage persistence
  const [selectedCity, setSelectedCity] = useState(getDefaultCity);

  // State to track available venue types for the selected city
  const [availableVenueTypes, setAvailableVenueTypes] = useState([]);

  // Get venue type from localStorage or first available for selected city as default
  const getDefaultVenueType = () => {
    const savedVenueType = localStorage.getItem("selectedVenueType");
    if (savedVenueType) {
      return savedVenueType;
    }
    if (venueData && venueData.images && venueData.images[selectedCity]) {
      const types = Object.keys(venueData.images[selectedCity]);
      if (types.length > 0) {
        return types[0];
      }
    }
    return "FARM HOUSE";
  };

  // State for selected venue type with localStorage persistence
  const [selectedVenueType, setSelectedVenueType] =
    useState(getDefaultVenueType);

  // Modal state for video playback
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [selectedVideoTitle, setSelectedVideoTitle] = useState("");

  // Fetch data from API
  useEffect(() => {
    const getVenueData = async () => {
      try {
        setLoading(true);
        const response = await fetchAllVenues();
        const processedData = processVenueData(response);
        setVenueData(processedData);
        setError(null);
      } catch (err) {
        console.error("Failed to fetch venue data:", err);
        setError("Failed to load venue data. Using fallback data.");
        setVenueData(data); // Use fallback data
      } finally {
        setLoading(false);
      }
    };

    getVenueData();
  }, []);

  // Save city selection to localStorage and update available venue types
  useEffect(() => {
    localStorage.setItem("selectedCity", selectedCity);

    // Update available venue types for the selected city
    if (venueData && venueData.images && venueData.images[selectedCity]) {
      const availableTypes = Object.keys(venueData.images[selectedCity]);
      setAvailableVenueTypes(availableTypes);

      // If current selected venue type is not available in this city, select the first available one
      if (
        availableTypes.length > 0 &&
        !availableTypes.includes(selectedVenueType)
      ) {
        setSelectedVenueType(availableTypes[0]);
      }
    }
  }, [selectedCity, venueData, selectedVenueType]);

  // When data is loaded, validate saved selections against available options
  useEffect(() => {
    if (!loading && venueData && venueData.cities && venueData.cities.length > 0) {
      // Validate saved city against available cities
      const availableCities = venueData.cities.map(city => city.name);
      if (!availableCities.includes(selectedCity)) {
        setSelectedCity(availableCities[0]);
      }
    }
  }, [loading, venueData, selectedCity]);

  // Save venue type selection to localStorage
  useEffect(() => {
    localStorage.setItem("selectedVenueType", selectedVenueType);
  }, [selectedVenueType]);

  // Save media type selection to localStorage
  useEffect(() => {
    localStorage.setItem("mediaType", mediaType);
  }, [mediaType]);

  // Add keyboard event listener to close modal with Escape key
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape" && isVideoModalOpen) {
        closeVideoModal();
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    // Cleanup
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [isVideoModalOpen]);

  const handleCitySelect = (city) => {
    setSelectedCity(city);
  };

  const handleVenueTypeSelect = (venueType) => {
    setSelectedVenueType(venueType);
  };

  useEffect(() => {
    if (uniqueName && availableVenueTypes.includes(uniqueName)) {
      handleVenueTypeSelect(uniqueName);
    }
  }, [uniqueName, availableVenueTypes]);

  const handleMediaTypeSelect = (type) => {
    setMediaType(type);
  };

  // Video modal functions
  const openVideoModal = (videoUrl, title) => {
    console.log("===>><<URL  ", videoUrl);
    setSelectedVideo(videoUrl);
    setSelectedVideoTitle(title);
    setIsVideoModalOpen(true);
    // Lock body scroll when modal is open
    document.body.style.overflow = "hidden";
  };

  const closeVideoModal = () => {
    setIsVideoModalOpen(false);
    setSelectedVideo(null);
    // Restore body scroll
    document.body.style.overflow = "auto";
  };

  // Filter venues based on selected media type
  const allVenues = venueData.images[selectedCity]?.[selectedVenueType] || [];

  const filteredVenues = allVenues.filter((venue) => {
    if (mediaType === "IMAGES") {
      // For IMAGES, only show venues with images
      return venue.hasImages || (venue.subImages && venue.subImages.length > 0);
    } else if (mediaType === "VIDEOS") {
      // For VIDEOS, only show venues with videos
      return venue.hasVideo || (venue.videos && venue.videos.length > 0);
    }
    return true;
  });


  // const getYouTubeVideoId = (url) => {
  //   try {
  //     const parsedUrl = new URL(url);
  //     if (parsedUrl.hostname === 'youtu.be') {
  //       return parsedUrl.pathname.slice(1);
  //     } else if (parsedUrl.hostname.includes('youtube.com')) {
  //       return parsedUrl.searchParams.get('v') || parsedUrl.pathname.split('/').pop();
  //     }
  //   } catch (err) {
  //     console.error("Invalid YouTube URL", url);
  //   }
  //   return null;
  // };

  const getEmbedUrl = (url) => {
    try {
      const parsed = new URL(url);
      let videoId;

      if (parsed.hostname === "youtu.be") {
        videoId = parsed.pathname.slice(1);
      } else if (parsed.hostname.includes("youtube.com")) {
        if (parsed.pathname.startsWith("/watch")) {
          videoId = parsed.searchParams.get("v");
        } else {
          videoId = parsed.pathname.split("/").pop();
        }
      }

      return videoId ? `https://www.youtube.com/embed/${videoId}?autoplay=1` : "";
    } catch (e) {
      console.error("Invalid YouTube URL:", url);
      return "";
    }
  };


  const getYoutubeVideoId = (url) => {
    try {
      const parsed = new URL(url);
      if (parsed.hostname === "youtu.be") {
        return parsed.pathname.slice(1);
      } else if (parsed.hostname.includes("youtube.com")) {
        if (parsed.pathname.startsWith("/watch")) {
          return parsed.searchParams.get("v");
        } else if (parsed.pathname.startsWith("/embed/") || parsed.pathname.startsWith("/shorts/")) {
          return parsed.pathname.split("/")[2];
        }
      }
    } catch (e) {
      console.error("Invalid YouTube URL:", url);
    }
    return null;
  };





  return (
    <>
      <div className="relative min-h-screen bg-[#fcf2e9]">
        {/* SVG as background */}
        <div
          className="absolute inset-0 bg-cover bg-center pointer-events-none z-0"
          style={{ backgroundImage: `url(${svg})` }}
        />

        {/* Content in front */}
        <div className="container mx-auto flex flex-col items-center justify-center min-h-screen px-4 py-8 relative z-10">
          <h2
            style={{
              transform: "rotateX(-28deg) rotateY(16deg)",
              // textShadow: "8px 8px 0px rgba(0,0,0,1)",
            }}
            className="font-Anton font-outline-black-1 sm:text-8xl text-6xl text-white my-20 text-center text-special"
          >
            <span className="uppercase">Which venue is</span>
            <br />
            <span className="uppercase">right for you?</span>
          </h2>



          {/* City selection */}
          <div className="flex text-center mb-4">
            {venueData.cities.map((city) => (
              <button
                key={city.name}
                onClick={() => handleCitySelect(city.name)}
                className={`sm:py-4 py-2 sm:px-10 px-4 text-xl rounded-full font-Anton tracking-wide m-1 border ${selectedCity === city.name
                  ? "border-[#baab8f]"
                  : "border-transparent"
                  }`}
                style={{
                  background: selectedCity === city.name ? "#ffffff" : "",
                }}
              >
                {city.name}
              </button>
            ))}
          </div>

          {/* Venue type selection */}
          <div className="flex text-center mb-4 bg-[#a29f9a] rounded-full">
            {venueData.venueTypes
              .filter((venueType) =>
                availableVenueTypes.includes(venueType.name)
              )
              .map((venueType) => (
                <button
                  key={venueType.name}
                  onClick={() => handleVenueTypeSelect(venueType.name)}
                  className={`sm:py-4 py-2 sm:px-10 px-2 sm:text-xl text-[14px] rounded-full font-Anton tracking-wide m-1 ${selectedVenueType === venueType.name
                    ? "bg-white border-4 border-[#6e6c6b]  "
                    : ""
                    }`}
                >
                  {venueType.name}
                </button>
              ))}
          </div>

          {/* Images and Videos buttons */}
          <div className="flex justify-center mb-6 gap-4">
            <button
              onClick={() => handleMediaTypeSelect("IMAGES")}
              className={`sm:py-4 py-2 sm:px-10 px-4 text-xl rounded-full font-Anton tracking-wide border border-[#baab8f] ${mediaType === "IMAGES" ? "bg-white" : "bg-transparent"
                }`}
            >
              IMAGES
            </button>
            <button
              onClick={() => handleMediaTypeSelect("VIDEOS")}
              className={`sm:py-4 py-2 sm:px-10 px-4 text-xl rounded-full font-Anton tracking-wide border border-[#baab8f] ${mediaType === "VIDEOS" ? "bg-white" : "bg-transparent"
                }`}
            >
              VIDEOS
            </button>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              {/* Beautiful loader that matches website theme */}
              <div className="flex flex-col items-center">
                <div className="relative w-24 h-24">
                  <div className="absolute top-0 left-0 w-full h-full border-4 border-[#c1ab8b] rounded-full animate-ping opacity-75"></div>
                  <div className="absolute top-2 left-2 w-20 h-20 border-4 border-[#eb936b] rounded-full animate-spin"></div>
                  <div className="absolute top-4 left-4 w-16 h-16 border-4 border-[#70b3a3] rounded-full animate-pulse"></div>
                  <div className="absolute top-6 left-6 w-12 h-12 border-4 border-[#f4cd77] rounded-full animate-bounce"></div>
                </div>
                <p className="text-xl font-Anton mt-4">Loading venues...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center h-64">
              <p className="text-xl font-Anton text-red-500">{error}</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-2">
              {filteredVenues.length === 0 ? (
                <div className="col-span-3 text-center py-10">
                  <p className="text-xl font-Anton">
                    {mediaType === "IMAGES"
                      ? "No images available for this selection."
                      : "No videos available for this selection."}
                  </p>
                </div>
              ) : mediaType === "VIDEOS" ? (
                // For videos, flatten all videos from all venues into individual grid items
                filteredVenues.flatMap((venue, venueIndex) =>
                  venue.videos?.map((videoUrl, videoIndex) => {
                    const videoId = getYoutubeVideoId(videoUrl);
                    // Initialize loading state for this video if not already set
                    const loadingKey = `venue-${venueIndex}-video-${videoIndex}`;
                    if (imageLoading[loadingKey] === undefined) {
                      setImageLoading((prev) => ({
                        ...prev,
                        [loadingKey]: true,
                      }));
                    }

                    return (
                      <div key={`video-${venueIndex}-${videoIndex}`} className="w-full">
                        <div
                          className="bg-gray-100 rounded-[30px] overflow-hidden shadow-md relative border-8 border-[#c1ab8b] hover:scale-90 transition-transform duration-300 xl:h-[60vh] md:h-[30vh] h-[30vh] cursor-pointer"
                          onClick={() =>
                            openVideoModal(
                              videoUrl,
                              `${venue.alt} - Video ${videoIndex + 1}`
                            )
                          }
                        >
                          {/* Show loader while media is loading */}
                          {imageLoading[loadingKey] && (
                            <div className="absolute inset-0 flex items-center justify-center z-10 bg-gray-100 bg-opacity-80">
                              <div className="w-16 h-16 border-4 border-[#c1ab8b] border-t-[#eb936b] rounded-full animate-spin"></div>
                            </div>
                          )}

                          <img
                            src={
                              videoId
                                ? `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`
                                : ""
                            }
                            alt={`${venue.alt} - Video ${videoIndex + 1}`}
                            className="w-full h-full object-cover"
                            onLoad={() =>
                              setImageLoading((prev) => ({
                                ...prev,
                                [loadingKey]: false,
                              }))
                            }
                            onError={() => {
                              setImageLoading((prev) => ({
                                ...prev,
                                [loadingKey]: false,
                              }));
                            }}
                          />

                          {/* Play button overlay */}
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="w-20 h-20 rounded-full bg-white bg-opacity-70 flex items-center justify-center shadow-lg transform transition-transform duration-300 hover:scale-110">
                              <div className="w-0 h-0 border-t-8 border-t-transparent border-l-12 border-l-[#eb936b] border-b-8 border-b-transparent ml-1"></div>
                            </div>
                          </div>

                          {/* Text overlay */}
                          <div className="absolute bottom-0 w-full bg-gradient-to-t from-black via-black/80 to-transparent text-white text-center py-3 px-2">
                            <p className="font-Anton text-xl font-bold tracking-wide uppercase drop-shadow-lg" style={{ textShadow: "2px 2px 4px rgba(0,0,0,0.8)" }}>{venue.info?.[0]?.state || venue.alt}</p>
                          </div>
                        </div>
                      </div>
                    );
                  }) || []
                )
              ) : (
                // For images, display normally
                filteredVenues.map((venue, index) => {
                  // Initialize loading state for this venue if not already set
                  if (imageLoading[`venue-${index}`] === undefined) {
                    setImageLoading((prev) => ({
                      ...prev,
                      [`venue-${index}`]: true,
                    }));
                  }

                  // Ensure image URL is properly formatted
                  const imageUrl =
                    venue.src && venue.src.startsWith("/")
                      ? `http://localhost:9000${venue.src}`
                      : venue.src;

                  // Process subImages to ensure proper URLs
                  const processedSubImages = venue.subImages?.map((img) => ({
                    ...img,
                    src:
                      img.src && img.src.startsWith("/")
                        ? `http://localhost:9000${img.src}`
                        : img.src,
                  }));

                  return (
                    <div key={index} className="w-full">
                      <Link
                        to={`/venue/venuedetails/${generateUniqueId(venue, index, selectedCity, selectedVenueType)}`}
                        state={{
                          city: selectedCity,
                          image: imageUrl,
                          title: venue.alt,
                          subImages: processedSubImages,
                          info: venue.info,
                          videos: venue.videos,
                          mediaType: mediaType, // Pass the selected media type
                          id: generateUniqueId(venue, index, selectedCity, selectedVenueType), // Also pass ID in state for backward compatibility
                        }}
                      >
                        <div className="bg-gray-100 rounded-[30px] overflow-hidden shadow-md flex flex-col items-center justify-center relative border-8 border-[#c1ab8b] hover:scale-90 transition-transform duration-300 xl:h-[60vh] md:h-[30vh] h-[30vh]">
                          {/* Show loader while media is loading */}
                          {imageLoading[`venue-${index}`] && (
                            <div className="absolute inset-0 flex items-center justify-center z-10 bg-gray-100 bg-opacity-80">
                              <div className="w-16 h-16 border-4 border-[#c1ab8b] border-t-[#eb936b] rounded-full animate-spin"></div>
                            </div>
                          )}

                          <img
                            src={imageUrl}
                            alt={venue.alt}
                            className="w-full h-full object-cover"
                            onLoad={() =>
                              setImageLoading((prev) => ({
                                ...prev,
                                [`venue-${index}`]: false,
                              }))
                            }
                            onError={() => {
                              // Handle image error - set loading to false and use fallback image
                              setImageLoading((prev) => ({
                                ...prev,
                                [`venue-${index}`]: false,
                              }));
                            }}
                          />

                          {/* Text overlay */}
                          <div className="absolute bottom-0 w-full bg-gradient-to-t from-black via-black/80 to-transparent text-white text-center py-3 px-2">
                            <p className="font-Anton text-xl font-bold tracking-wide uppercase drop-shadow-lg" style={{ textShadow: "2px 2px 4px rgba(0,0,0,0.8)" }}>{venue.info?.[0]?.state || venue.alt}</p>
                          </div>
                        </div>
                      </Link>
                    </div>
                  );
                })
              )}
            </div>
          )}

          {/* <div className="flex flex-col justify-center items-center my-4">
            <p className="text-center font-Archivo text-[16.41px] w-[86%] font-normal leading-[30.6px] tracking-[1px]">
              There are many variations of passages of Lorem Ipsum available,
              but the majority.
            </p>
          </div>


          <button className="relative shadow-custom px-8 py-2 bg-white border border-black font-normal tracking-wide rounded-sm font-Anton transition-all duration-300 hover:scale-105 hover:shadow-xl hover:-translate-y-1 group ">
            READ MORE
          </button> */}
        </div>
      </div>
      {/* Video Modal */}
      {isVideoModalOpen && (
        <div
          className="fixed top-0 left-0 w-full h-screen bg-black bg-opacity-70 flex justify-center items-center z-50 backdrop-blur-sm"
          onClick={closeVideoModal}
        >
          <div
            className="bg-[#fcf2e9] p-6 rounded-xl max-w-4xl w-full max-h-screen overflow-auto shadow-2xl transform transition-all duration-300 ease-in-out relative"
            style={{
              boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.5)",
              animation: "modalFadeIn 0.3s ease-out forwards",
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Video content with beautiful styling */}
            <div className="w-full h-[500px] flex items-center justify-center bg-black relative rounded-lg overflow-hidden">
              {/* Decorative border */}
              <div className="absolute inset-0 border-8 border-[#c1ab8b] pointer-events-none z-20 rounded-lg"></div>

              {/* Decorative corner accents */}
              <div className="absolute top-0 left-0 w-12 h-12 border-t-8 border-l-8 border-[#eb936b] z-30 rounded-tl-lg"></div>
              <div className="absolute top-0 right-0 w-12 h-12 border-t-8 border-r-8 border-[#eb936b] z-30 rounded-tr-lg"></div>
              <div className="absolute bottom-0 left-0 w-12 h-12 border-b-8 border-l-8 border-[#eb936b] z-30 rounded-bl-lg"></div>
              <div className="absolute bottom-0 right-0 w-12 h-12 border-b-8 border-r-8 border-[#eb936b] z-30 rounded-br-lg"></div>

              {/* Video loader with enhanced styling */}
              <div
                className="absolute inset-0 flex items-center justify-center bg-black z-10"
                id="video-loader"
              >
                <div className="flex flex-col items-center">
                  <div className="relative w-24 h-24">
                    <div className="absolute top-0 left-0 w-full h-full border-4 border-[#c1ab8b] rounded-full animate-ping opacity-75"></div>
                    <div className="absolute top-2 left-2 w-20 h-20 border-4 border-[#eb936b] rounded-full animate-spin"></div>
                    <div className="absolute top-4 left-4 w-16 h-16 border-4 border-[#70b3a3] rounded-full animate-pulse"></div>
                    <div className="absolute top-6 left-6 w-12 h-12 border-4 border-[#f4cd77] rounded-full animate-bounce"></div>
                  </div>
                  <p className="text-xl font-Anton mt-4 text-white">
                    Loading your video...
                  </p>
                </div>
              </div>

              <iframe
                src={selectedVideo ? getEmbedUrl(selectedVideo) : ""}
                title={selectedVideoTitle}
                className="w-full h-full z-5"
                allowFullScreen
                allow="autoplay; encrypted-media"
                onLoad={() => {
                  const loader = document.getElementById("video-loader");
                  if (loader) loader.style.display = "none";
                }}
              ></iframe>


              {/* Close button for video */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  closeVideoModal();
                }}
                className="absolute top-4 right-4 p-2 h-[40px] w-[40px] bg-white bg-opacity-80 text-black font-bold rounded-full shadow-lg hover:bg-opacity-100 transition-all duration-300 transform hover:scale-110 flex items-center justify-center z-50"
                aria-label="Close video"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={3}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Video title */}
            <h3 className="text-2xl font-Anton text-center mt-4">
              {selectedVideoTitle}
            </h3>

            <button
              onClick={(e) => {
                e.stopPropagation();
                closeVideoModal();
              }}
              className="absolute top-4 right-4 p-2 h-[50px] w-[50px] bg-[#eb936b] text-white font-bold rounded-full shadow-lg hover:bg-[#c1ab8b] transition-all duration-300 transform hover:scale-110 flex items-center justify-center z-50"
              aria-label="Close modal"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={3}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      )}
    </>
  );
};

// Add animation keyframes
const modalStyles = document.createElement("style");
modalStyles.innerHTML = `
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(10px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }
`;
document.head.appendChild(modalStyles);

export default PreferredVenue;
