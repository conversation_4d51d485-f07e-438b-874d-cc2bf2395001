import React, { useEffect, useState } from "react";
import { VelocityScroll } from "../../components/ui/DiscoverEventvelocitytext";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/autoplay";
import { Autoplay } from "swiper/modules";
import slider1 from "../../assets/mehndiDiscover.png";
import slider2 from "../../assets/ringcermonyDiscover.png";
import slider3 from "../../assets/cocktailDiscover.png";
import slider4 from "../../assets/haldiDiscover.png";
import mobileSlider1 from "../../assets/mehndiDiscover_sm.png";
import mobileSlider2 from "../../assets/ringcermonyDiscover_sm.png";
import mobileSlider3 from "../../assets/cocktailDiscover_sm.png";
import mobileSlider4 from "../../assets/haldiDiscover_sm.png";
import { Link } from "react-router-dom";

export function DiscoverEvents() {
  const textw = [
    "Mehndi Function",
    "Ring Ceremony",
    "Cocktail Party",
    "Haldi Function",
  ];

  // Default desktop images
  const desktopImages = [slider1, slider2, slider3, slider4];
  const mobileImages = [mobileSlider1, mobileSlider2, mobileSlider3, mobileSlider4];

  const [activeIndex, setActiveIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // Detect screen size on resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const images = isMobile ? mobileImages : desktopImages;

  const backgroundColors = ["#8bc6ec", "#ffc3a0", "#A9A9A9", "#7d915c"];

  return (
    <div
      className="w-screen lg:h-[800px] h-[800px]  relative overflow-hidden "
      style={{ backgroundColor: backgroundColors[activeIndex] }}
    >
      {/* Centered Heading */}
      <div className="absolute top-4 md:top-[5%] w-full text-center z-20 px-4 mt-12 sm:mt-0">
        <h2 className="font-Anton text-4xl sm:text-6xl md:text-7xl lg:text-7xl text-black">
          DISCOVER
        </h2>
        <h2 className="font-Anton text-4xl sm:text-6xl md:text-7xl lg:text-7xl text-black">
          ALL OUR EVENTS
        </h2>
      </div>

      {/* Diagonal Velocity Scroll */}
      <div className="absolute inset-0 z-10 flex items-center justify-center overflow-hidden transform -rotate-45">
        <VelocityScroll
          text={textw[activeIndex]}
          default_velocity={2}
          className="text-[80px] sm:text-[120px] md:text-[180px] lg:text-[240px] font-bold tracking-[-0.02em] text-black drop-shadow-sm"
        />
      </div>

      {/* Foreground Auto-Moving Slider */}
      <div className="absolute inset-0 lg:mt-12  z-20 flex items-center justify-center ">
        <div className=" mt-10 md:mt-[10.5rem] lg:mt-20 z-20 w-full px-4">
          <Swiper
            modules={[Autoplay]}
            spaceBetween={50}
            slidesPerView={1}
            autoplay={{ delay: 5000, disableOnInteraction: false }}
            loop={true}
            className="max-w-[60%] max-h-[90%] w-full h-full"
            onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
          >
            {images.map((image, index) => (
              <SwiperSlide key={index}>
                <Link to="/events">
                  <div className="flex flex-col items-center justify-center text-white text-3xl font-bold rounded-2xl h-full sm:mt-0 mt-10">
                    <img
                      src={image}
                      alt={`Slide ${index + 1}`}
                      className="object-cover rounded-2xl w-full h-[400px] "
                    />
                    <h1 className="text-center font-Anton text-3xl md:text-6xl lg:text-5xl font-normal leading-[35px] tracking-[1px] text-black stroke-slate-500 mt-4 font-outline-white-1 break-words px-4">
                      {textw[activeIndex]}
                    </h1>
                  </div>
                </Link>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </div>
  );
}
