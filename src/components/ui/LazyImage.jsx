import React, { useState, useEffect, useCallback, useRef } from "react";

/**
 * Optimized LazyImage component for better performance
 * Features:
 * - Intersection Observer for lazy loading
 * - Loading states with spinners
 * - Error handling with fallback UI
 * - Native lazy loading as fallback
 * - Async decoding for better performance
 */
const LazyImage = ({ 
  src, 
  alt, 
  className = "", 
  onClick, 
  onLoad, 
  onError,
  loadingClassName = "w-12 h-12 border-4 border-[#c1ab8b] border-t-[#eb936b] rounded-full animate-spin",
  errorFallback = null,
  rootMargin = "50px",
  threshold = 0.1,
  ...props 
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin, // Start loading before the image comes into view
        threshold
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [rootMargin, threshold]);

  const handleImageLoad = useCallback(() => {
    setIsLoading(false);
    onLoad && onLoad();
  }, [onLoad]);

  const handleImageError = useCallback(() => {
    setIsLoading(false);
    setHasError(true);
    onError && onError();
  }, [onError]);

  // Default error fallback
  const defaultErrorFallback = (
    <div className="absolute inset-0 flex items-center justify-center bg-gray-200 z-10">
      <div className="text-center text-gray-500">
        <div className="text-2xl mb-2">📷</div>
        <p className="text-sm">Image unavailable</p>
      </div>
    </div>
  );

  return (
    <div ref={imgRef} className={`relative ${className}`} {...props}>
      {/* Loading placeholder */}
      {isLoading && isInView && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
          <div className={loadingClassName}></div>
        </div>
      )}

      {/* Error fallback */}
      {hasError && (
        errorFallback || defaultErrorFallback
      )}

      {/* Actual image - only load when in view */}
      {isInView && !hasError && (
        <img
          src={src}
          alt={alt}
          className="w-full h-full object-cover cursor-pointer"
          onClick={onClick}
          onLoad={handleImageLoad}
          onError={handleImageError}
          loading="lazy" // Native lazy loading as fallback
          decoding="async" // Async decoding for better performance
        />
      )}

      {/* Placeholder when not in view */}
      {!isInView && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
          <div className="text-gray-400 text-sm">Loading...</div>
        </div>
      )}
    </div>
  );
};

export default LazyImage;
