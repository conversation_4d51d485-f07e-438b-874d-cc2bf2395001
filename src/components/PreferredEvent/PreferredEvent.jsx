import React, { useState, useEffect } from "react";
import svg from "../../assets/illu-rainbow-wave.svg.svg";
import { Link, useLocation } from "react-router-dom";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/autoplay";
import { Autoplay, Navigation } from "swiper/modules";
import data from "../../assets/EventsData.js"; // Keep for fallback
import { fetchAllEvents, processEventData } from "../../services/api.js";

// Function to generate a unique ID for each event
const generateUniqueId = (event, index, city, eventType) => {
  // Create a slug from event properties
  const citySlug = city ? city.toLowerCase().replace(/\s+/g, "-") : "";
  const typeSlug = eventType
    ? eventType.toLowerCase().replace(/\s+/g, "-")
    : "";
  const titleSlug = event.alt
    ? event.alt.toLowerCase().replace(/\s+/g, "-")
    : "";

  // Combine multiple properties to ensure uniqueness
  const uniqueId = `${citySlug}-${typeSlug}-${titleSlug}-${index}`;

  // Make URL-friendly by removing special characters
  return uniqueId.replace(/[^a-z0-9-]/g, "");
};

// Add modal animation styles
const modalStyles = `
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(10px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }
`;

const PreferredEvent = () => {
  const location = useLocation();
  const { name } = location.state || {};

  // Initialize data states
  const [eventData, setEventData] = useState(data);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [imageLoading, setImageLoading] = useState({});

  // Get saved media type from localStorage or default to IMAGES
  const getSavedMediaType = () => {
    const savedMediaType = localStorage.getItem("mediaType");
    return savedMediaType || "IMAGES";
  };

  // State for media type with localStorage persistence
  const [mediaType, setMediaType] = useState(getSavedMediaType);

  // Get first city as default if available, otherwise use saved value or NEW DELHI
  const getDefaultCity = () => {
    const savedCity = localStorage.getItem("selectedCity");
    if (savedCity) {
      return savedCity;
    }
    if (eventData && eventData.cities && eventData.cities.length > 0) {
      return eventData.cities[0].name;
    }
    return "NEW DELHI";
  };

  // State for selected city with localStorage persistence
  const [selectedCity, setSelectedCity] = useState(getDefaultCity);

  // State to track available event types for the selected city
  const [availableEventTypes, setAvailableEventTypes] = useState([]);

  // Get event type from localStorage or first available for selected city as default
  const getDefaultEventType = () => {
    const savedEventType = localStorage.getItem("selectedEventType");
    if (savedEventType) {
      return savedEventType;
    }
    if (eventData && eventData.images && eventData.images[selectedCity]) {
      const types = Object.keys(eventData.images[selectedCity]);
      if (types.length > 0) {
        return types[0];
      }
    }
    return "BIRTHDAY PARTY";
  };

  // State for selected event type with localStorage persistence
  const [selectedEventType, setSelectedEventType] =
    useState(getDefaultEventType);

  // Modal state for video playback
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [selectedVideoTitle, setSelectedVideoTitle] = useState("");

  // Add animation styles to document head
  useEffect(() => {
    // Create style element
    const styleEl = document.createElement("style");
    // No need to set type attribute as it's the default
    styleEl.innerHTML = modalStyles;
    document.head.appendChild(styleEl);

    // Cleanup on component unmount
    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);

  // Fetch data from API
  useEffect(() => {
    const getEventData = async () => {
      try {
        setLoading(true);
        const response = await fetchAllEvents();
        const processedData = processEventData(response);
        setEventData(processedData);
        setError(null);
      } catch (err) {
        console.error("Failed to fetch event data:", err);
        setError("Failed to load event data. Using fallback data.");
        setEventData(data); // Use fallback data
      } finally {
        setLoading(false);
      }
    };

    getEventData();
  }, []);

  // Save city selection to localStorage and update available event types
  useEffect(() => {
    localStorage.setItem("selectedCity", selectedCity);

    // Update available event types for the selected city
    if (eventData && eventData.images && eventData.images[selectedCity]) {
      const availableTypes = Object.keys(eventData.images[selectedCity]);
      setAvailableEventTypes(availableTypes);

      // If current selected event type is not available in this city, select the first available one
      if (
        availableTypes.length > 0 &&
        !availableTypes.includes(selectedEventType)
      ) {
        setSelectedEventType(availableTypes[0]);
      }
    }
  }, [selectedCity, eventData, selectedEventType]);

  // When data is loaded, validate saved selections against available options
  useEffect(() => {
    if (
      !loading &&
      eventData &&
      eventData.cities &&
      eventData.cities.length > 0
    ) {
      // Validate saved city against available cities
      const availableCities = eventData.cities.map((city) => city.name);
      if (!availableCities.includes(selectedCity)) {
        setSelectedCity(availableCities[0]);
      }
    }
  }, [loading, eventData, selectedCity]);

  // Save event type selection to localStorage
  useEffect(() => {
    localStorage.setItem("selectedEventType", selectedEventType);
  }, [selectedEventType]);

  // Save media type selection to localStorage
  useEffect(() => {
    localStorage.setItem("mediaType", mediaType);
  }, [mediaType]);

  // Add keyboard event listener to close modal with Escape key
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape" && isVideoModalOpen) {
        closeVideoModal();
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    // Cleanup
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [isVideoModalOpen]);

  const handleCitySelect = (city) => {
    setSelectedCity(city);
  };

  const handleEventTypeSelect = (eventType) => {
    setSelectedEventType(eventType);
  };

  const handleMediaTypeSelect = (type) => {
    setMediaType(type);
  };

  useEffect(() => {
    if (name && availableEventTypes.includes(name)) {
      handleEventTypeSelect(name);
    }
  }, [name, availableEventTypes]);

  // Video modal functions
  const openVideoModal = (videoUrl, title) => {
    setSelectedVideo(videoUrl);
    setSelectedVideoTitle(title);
    setIsVideoModalOpen(true);
    // Lock body scroll when modal is open
    document.body.style.overflow = "hidden";
  };

  const closeVideoModal = () => {
    setIsVideoModalOpen(false);
    setSelectedVideo(null);
    // Restore body scroll
    document.body.style.overflow = "auto";
  };

  // Filter events based on selected media type
  const allEvents = eventData.images[selectedCity]?.[selectedEventType] || [];

  const filteredEvents = allEvents.filter((event) => {
    if (mediaType === "IMAGES") {
      // For IMAGES, only show events with images
      return event.hasImages || (event.subImages && event.subImages.length > 0);
    } else if (mediaType === "VIDEOS") {
      // For VIDEOS, only show events with videos
      return event.hasVideo || (event.videos && event.videos.length > 0);
    }
    return true;
  });

  return (
    <>
      <div className="relative min-h-screen bg-[#fcf2e9]">
        {/* SVG as background */}
        <div
          className="absolute inset-0 bg-cover bg-center pointer-events-none z-0"
          style={{ backgroundImage: `url(${svg})` }}
        />

        {/* Content in front */}
        <div className="container mx-auto flex flex-col items-center justify-center min-h-screen px-4 py-8 relative z-10">
          <h2
            style={{
              transform: "rotateX(-28deg) rotateY(16deg)",
              // textShadow: "8px 8px 0px rgba(0,0,0,1)",
            }}
            className="font-Anton font-outline-black-1 sm:text-8xl text-6xl text-white my-20 text-center text-special"
          >
            <span className="uppercase">Which event is</span>
            <br />
            <span className="uppercase">right for you?</span>
          </h2>

          {/* City selection */}
          <div className="flex text-center mb-4">
            {eventData.cities.map((city) => (
              <button
                key={city.name}
                onClick={() => handleCitySelect(city.name)}
                className={`sm:py-4 py-2 sm:px-10 px-4 text-xl rounded-full font-Anton tracking-wide m-1 border ${
                  selectedCity === city.name
                    ? "border-[#baab8f]"
                    : "border-transparent"
                }`}
                style={{
                  background: selectedCity === city.name ? "#ffffff" : "",
                }}
              >
                {city.name}
              </button>
            ))}
          </div>

          {/* Event type selection */}
          <div className="flex text-center mb-4 bg-[#a29f9a] rounded-full">
            {eventData.eventTypes
              .filter((eventType) =>
                availableEventTypes.includes(eventType.name)
              )
              .map((eventType) => (
                <button
                  key={eventType.name}
                  onClick={() => handleEventTypeSelect(eventType.name)}
                  className={`sm:py-4 py-2 sm:px-10 px-2 sm:text-xl text-[14px] rounded-full font-Anton tracking-wide m-1 ${
                    selectedEventType === eventType.name
                      ? "bg-white border-4 border-[#6e6c6b]  "
                      : ""
                  }`}
                >
                  {eventType.name}
                </button>
              ))}
          </div>

          {/* Images and Videos buttons */}
          <div className="flex justify-center mb-6 gap-4">
            <button
              onClick={() => handleMediaTypeSelect("IMAGES")}
              className={`sm:py-4 py-2 sm:px-10 px-4 text-xl rounded-full font-Anton tracking-wide border border-[#baab8f] ${
                mediaType === "IMAGES" ? "bg-white" : "bg-transparent"
              }`}
            >
              IMAGES
            </button>
            <button
              onClick={() => handleMediaTypeSelect("VIDEOS")}
              className={`sm:py-4 py-2 sm:px-10 px-4 text-xl rounded-full font-Anton tracking-wide border border-[#baab8f] ${
                mediaType === "VIDEOS" ? "bg-white" : "bg-transparent"
              }`}
            >
              VIDEOS
            </button>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              {/* Beautiful loader that matches website theme */}
              <div className="flex flex-col items-center">
                <div className="relative w-24 h-24">
                  <div className="absolute top-0 left-0 w-full h-full border-4 border-[#c1ab8b] rounded-full animate-ping opacity-75"></div>
                  <div className="absolute top-2 left-2 w-20 h-20 border-4 border-[#eb936b] rounded-full animate-spin"></div>
                  <div className="absolute top-4 left-4 w-16 h-16 border-4 border-[#70b3a3] rounded-full animate-pulse"></div>
                  <div className="absolute top-6 left-6 w-12 h-12 border-4 border-[#f4cd77] rounded-full animate-bounce"></div>
                </div>
                <p className="text-xl font-Anton mt-4">Loading events...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center h-64">
              <p className="text-xl font-Anton text-red-500">{error}</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-2">
              {filteredEvents.length === 0 ? (
                <div className="col-span-3 text-center py-10">
                  <p className="text-xl font-Anton">
                    {mediaType === "IMAGES"
                      ? "No images available for this selection."
                      : "No videos available for this selection."}
                  </p>
                </div>
              ) : mediaType === "VIDEOS" ? (
                // For videos, flatten all videos from all events into individual grid items
                filteredEvents.flatMap(
                  (event, eventIndex) =>
                    event.videos?.map((videoUrl, videoIndex) => {
                      // Initialize loading state for this video if not already set
                      const loadingKey = `event-${eventIndex}-video-${videoIndex}`;
                      if (imageLoading[loadingKey] === undefined) {
                        setImageLoading((prev) => ({
                          ...prev,
                          [loadingKey]: true,
                        }));
                      }

                      return (
                        <div
                          key={`video-${eventIndex}-${videoIndex}`}
                          className="w-full"
                        >
                          <div
                            className="bg-gray-100 rounded-[30px] overflow-hidden shadow-md relative border-8 border-[#c1ab8b] hover:scale-90 transition-transform duration-300 xl:h-[60vh] md:h-[30vh] h-[30vh] cursor-pointer"
                            onClick={() =>
                              openVideoModal(
                                videoUrl,
                                `${event.alt} - Video ${videoIndex + 1}`
                              )
                            }
                          >
                            {/* Show loader while media is loading */}
                            {imageLoading[loadingKey] && (
                              <div className="absolute inset-0 flex items-center justify-center z-10 bg-gray-100 bg-opacity-80">
                                <div className="w-16 h-16 border-4 border-[#c1ab8b] border-t-[#eb936b] rounded-full animate-spin"></div>
                              </div>
                            )}

                            <img
                              src={`https://img.youtube.com/vi/${
                                videoUrl.split("v=")[1] ||
                                videoUrl.split("/").pop()
                              }/hqdefault.jpg`}
                              alt={`${event.alt} - Video ${videoIndex + 1}`}
                              className="w-full h-full object-cover"
                              onLoad={() =>
                                setImageLoading((prev) => ({
                                  ...prev,
                                  [loadingKey]: false,
                                }))
                              }
                              onError={() => {
                                setImageLoading((prev) => ({
                                  ...prev,
                                  [loadingKey]: false,
                                }));
                              }}
                            />

                            {/* Play button overlay */}
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="w-20 h-20 rounded-full bg-white bg-opacity-70 flex items-center justify-center shadow-lg transform transition-transform duration-300 hover:scale-110">
                                <div className="w-0 h-0 border-t-8 border-t-transparent border-l-12 border-l-[#eb936b] border-b-8 border-b-transparent ml-1"></div>
                              </div>
                            </div>

                            {/* Text overlay */}
                            <div className="absolute bottom-0 w-full bg-gradient-to-t from-black via-black/80 to-transparent text-white text-center py-3 px-2">
                              <p
                                className="font-Anton text-xl font-bold tracking-wide uppercase drop-shadow-lg"
                                style={{
                                  textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
                                }}
                              >
                                {event.info?.[0]?.state || event.alt}
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    }) || []
                )
              ) : (
                // For images, display normally
                filteredEvents.map((event, index) => {
                  // Initialize loading state for this event if not already set
                  if (imageLoading[`event-${index}`] === undefined) {
                    setImageLoading((prev) => ({
                      ...prev,
                      [`event-${index}`]: true,
                    }));
                  }

                  // Ensure image URL is properly formatted
                  const imageUrl =
                    event.src && event.src.startsWith("/")
                      ? `http://localhost:9000${event.src}`
                      : event.src;

                  // Process subImages to ensure proper URLs
                  const processedSubImages = event.subImages?.map((img) => ({
                    ...img,
                    src:
                      img.src && img.src.startsWith("/")
                        ? `http://localhost:9000${img.src}`
                        : img.src,
                  }));

                  return (
                    <div key={index} className="w-full">
                      <Link
                        to={`/events/eventsdetails/${generateUniqueId(
                          event,
                          index,
                          selectedCity,
                          selectedEventType
                        )}`}
                        state={{
                          image: imageUrl,
                          title: event.alt,
                          subImages: processedSubImages,
                          info: event.info,
                          videos: event.videos,
                          hasVideo: event.hasVideo,
                          mediaType: "IMAGES", // Force IMAGES mode for image clicks
                          id: generateUniqueId(
                            event,
                            index,
                            selectedCity,
                            selectedEventType
                          ), // Also pass ID in state for backward compatibility
                        }}
                      >
                        <div className="bg-gray-100 rounded-[30px] overflow-hidden shadow-md flex flex-col items-center justify-center relative border-8 border-[#c1ab8b] hover:scale-90 transition-transform duration-300 xl:h-[60vh] md:h-[30vh] h-[30vh]">
                          {/* Show loader while media is loading */}
                          {imageLoading[`event-${index}`] && (
                            <div className="absolute inset-0 flex items-center justify-center z-10 bg-gray-100 bg-opacity-80">
                              <div className="w-16 h-16 border-4 border-[#c1ab8b] border-t-[#eb936b] rounded-full animate-spin"></div>
                            </div>
                          )}

                          <img
                            src={imageUrl}
                            alt={event.alt}
                            className="w-full h-full object-cover"
                            onLoad={() =>
                              setImageLoading((prev) => ({
                                ...prev,
                                [`event-${index}`]: false,
                              }))
                            }
                            onError={() => {
                              // Handle image error - set loading to false and use fallback image
                              setImageLoading((prev) => ({
                                ...prev,
                                [`event-${index}`]: false,
                              }));
                            }}
                          />

                          {/* Text overlay */}
                          <div className="absolute bottom-0 w-full bg-gradient-to-t from-black via-black/80 to-transparent text-white text-center py-3 px-2">
                            <p
                              className="font-Anton text-xl font-bold tracking-wide uppercase drop-shadow-lg"
                              style={{
                                textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
                              }}
                            >
                              {event.info?.[0]?.state || event.alt}
                            </p>
                          </div>
                        </div>
                      </Link>
                    </div>
                  );
                })
              )}
            </div>
          )}

          {/* Mobile view with Swiper - only show if there are events */}
          {!loading && !error && filteredEvents.length > 0 && (
            <div className="md:hidden w-full px-4 mt-4">
              <Swiper
                modules={[Autoplay, Navigation]}
                autoplay={{ delay: 3000, disableOnInteraction: false }}
                navigation
                loop={true}
                spaceBetween={30}
                slidesPerView={1}
                speed={500}
                className="w-full"
              >
                {filteredEvents.map((event, index) => (
                  <SwiperSlide key={index}>
                    {mediaType === "VIDEOS" &&
                    event.videos &&
                    event.videos.length > 0 ? (
                      // Display videos in a horizontal swiper for mobile
                      <div className="w-full px-2">
                        {/* Display videos side by side for mobile */}
                        <div className="grid grid-cols-1 gap-4 pb-2">
                          {event.videos.map((videoUrl, videoIndex) => (
                            <div
                              key={`mobile-video-${index}-${videoIndex}`}
                              className="bg-gray-100 rounded-[30px] overflow-hidden shadow-md relative border-8 border-[#c1ab8b] hover:scale-90 transition-transform duration-300 h-[300px] cursor-pointer"
                              onClick={() =>
                                openVideoModal(
                                  videoUrl,
                                  `${event.alt} - Video ${videoIndex + 1}`
                                )
                              }
                            >
                              <img
                                src={`https://img.youtube.com/vi/${
                                  videoUrl.split("v=")[1] ||
                                  videoUrl.split("/").pop()
                                }/hqdefault.jpg`}
                                alt={`${event.alt} - Video ${videoIndex + 1}`}
                                className="w-full h-full object-cover"
                              />

                              {/* Play button overlay */}
                              <div className="absolute inset-0 flex items-center justify-center">
                                <div className="w-20 h-20 rounded-full bg-white bg-opacity-70 flex items-center justify-center shadow-lg transform transition-transform duration-300 hover:scale-110">
                                  <div className="w-0 h-0 border-t-8 border-t-transparent border-l-12 border-l-[#eb936b] border-b-8 border-b-transparent ml-1"></div>
                                </div>
                              </div>

                              {/* Text overlay */}
                              <div className="absolute bottom-0 w-full bg-gradient-to-t from-black via-black/80 to-transparent text-white text-center py-3 px-2">
                                <p
                                  className="font-Anton text-xl font-bold tracking-wide uppercase drop-shadow-lg"
                                  style={{
                                    textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
                                  }}
                                >
                                  {event.info?.[0]?.state || event.alt}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      // For images, use Link to navigate to EventsDetails page with ID in URL
                      <Link
                        to={`/events/eventsdetails/${generateUniqueId(
                          event,
                          index,
                          selectedCity,
                          selectedEventType
                        )}`}
                        state={{
                          image: event.src,
                          title: event.alt,
                          subImages: event.subImages,
                          info: event.info,
                          videos: event.videos,
                          hasVideo: event.hasVideo,
                          mediaType: "IMAGES", // Force IMAGES mode for image clicks
                          id: generateUniqueId(
                            event,
                            index,
                            selectedCity,
                            selectedEventType
                          ), // Also pass ID in state for backward compatibility
                        }}
                      >
                        <div className="bg-gray-100 rounded-[30px] overflow-hidden shadow-md h-[300px] flex flex-col items-center justify-center relative border-8 border-[#c1ab8b]">
                          <img
                            src={event.src}
                            alt={event.alt}
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute bottom-0 w-full bg-gradient-to-t from-black via-black/80 to-transparent text-white text-center py-3 px-2">
                            <p
                              className="font-Anton text-lg font-bold tracking-wide uppercase drop-shadow-lg"
                              style={{
                                textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
                              }}
                            >
                              {event.info?.[0]?.state || event.alt}
                            </p>
                          </div>
                        </div>
                      </Link>
                    )}
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
          )}
        </div>
      </div>

      {/* Video Modal */}
      {isVideoModalOpen && (
        <div
          className="fixed top-0 left-0 w-full h-screen bg-black bg-opacity-70 flex justify-center items-center z-50 backdrop-blur-sm"
          onClick={closeVideoModal}
        >
          <div
            className="bg-[#fcf2e9] p-6 rounded-xl max-w-4xl w-full max-h-screen overflow-auto shadow-2xl transform transition-all duration-300 ease-in-out relative"
            style={{
              boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.5)",
              animation: "modalFadeIn 0.3s ease-out forwards",
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Video content with beautiful styling */}
            <div className="w-full h-[500px] flex items-center justify-center bg-black relative rounded-lg overflow-hidden">
              {/* Decorative border */}
              <div className="absolute inset-0 border-8 border-[#c1ab8b] pointer-events-none z-20 rounded-lg"></div>

              {/* Video loader with enhanced styling */}
              <div
                className="absolute inset-0 flex items-center justify-center bg-black z-10"
                id="video-loader"
              >
                <div className="flex flex-col items-center">
                  <div className="relative w-24 h-24">
                    <div className="absolute top-0 left-0 w-full h-full border-4 border-[#c1ab8b] rounded-full animate-ping opacity-75"></div>
                    <div className="absolute top-2 left-2 w-20 h-20 border-4 border-[#eb936b] rounded-full animate-spin"></div>
                    <div className="absolute top-4 left-4 w-16 h-16 border-4 border-[#70b3a3] rounded-full animate-pulse"></div>
                    <div className="absolute top-6 left-6 w-12 h-12 border-4 border-[#f4cd77] rounded-full animate-bounce"></div>
                  </div>
                  <p className="text-xl font-Anton mt-4 text-white">
                    Loading your video...
                  </p>
                </div>
              </div>

              <iframe
                src={
                  selectedVideo
                    ? selectedVideo.replace("watch?v=", "embed/") +
                      "?autoplay=1"
                    : ""
                }
                title={selectedVideoTitle}
                className="w-full h-full z-5"
                allowFullScreen
                allow="autoplay; encrypted-media"
                onLoad={() => {
                  // Hide loader when video is loaded
                  const loader = document.getElementById("video-loader");
                  if (loader) loader.style.display = "none";
                }}
              ></iframe>
            </div>

            {/* Close button with beautiful styling */}
            <button
              onClick={(e) => {
                e.stopPropagation(); // Stop event from bubbling up
                closeVideoModal();
              }}
              className="absolute top-4 right-4 p-2 h-[50px] w-[50px] bg-[#eb936b] text-white font-bold rounded-full shadow-lg hover:bg-[#c1ab8b] transition-all duration-300 transform hover:scale-110 flex items-center justify-center z-50"
              aria-label="Close modal"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={3}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default PreferredEvent;
