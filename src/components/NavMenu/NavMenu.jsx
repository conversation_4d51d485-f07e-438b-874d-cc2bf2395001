import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import React, { useEffect, useState } from "react";
import Logo from "../../assets/urbanvenuelogo.png";
import team1 from "../../assets/1evedet.png";
import team2 from "../../assets/2evedet.png";
import team3 from "../../assets/3evedet.png";
import team4 from "../../assets/4evedet.png";
import { Link, useNavigate } from "react-router-dom";
import { ABOUT, HOME, VENUE, BLOG, EVENTS, CAREER } from "@/routes/routes";
import FacebookOutlinedIcon from "@mui/icons-material/FacebookOutlined";
import InstagramIcon from "@mui/icons-material/Instagram";
import YouTubeIcon from "@mui/icons-material/YouTube";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import PinterestIcon from "@mui/icons-material/Pinterest";
import { CONTACT_US } from "@/routes/routes";

export default function NavMenu() {
  const navigate = useNavigate();
  const [open, setOpen] = useState(true);

  useEffect(() => {
    setOpen(true);
  }, []);

  const redirect = (path) => {
    setOpen((prev) => !prev);
    if (path === "/blog") {
      window.location.href = "/blog";
    } else {
      navigate(path);
    }
  };

  return (
    <>
      <div
        className="rounded-full 
             w-10 h-10 
             sm:w-14 sm:h-14 
             md:w-16 md:h-16 
             bg-black 
             fixed top-4 right-4 
             sm:top-6 sm:right-6 
             md:top-10 md:right-10 
             z-50 cursor-pointer 
             flex items-center justify-center"
        onClick={() => setOpen(!open)}
      >
        {open ? (
          <MenuIcon className="text-white text-base sm:text-lg md:text-xl" />
        ) : (
          <CloseIcon className="text-white text-base sm:text-lg md:text-xl" />
        )}
      </div>

      {
        <div
          className={`fixed flex top-0 left-0 w-full h-screen z-40 bg-[#fcf2e9] transition ${
            open ? "translate-x-full" : "translate-x-0"
          } duration-800 ease-in origin-left`}
        >
          <div className="flex flex-col flex-1 justify-between items-start p-4  ">
            <div onClick={() => redirect(HOME)} className="cursor-pointer ">
              <img src={Logo} className="w-[10rem] lg:w-30" alt="logo" />
            </div>

            <div className="flex flex-col justify-start items-start gap-4">
              {[
                { text: "Home", onClick: () => redirect(HOME) },
                { text: "Venue", onClick: () => redirect(VENUE) },
                { text: "About Us", onClick: () => redirect(ABOUT) },
                { text: "Events", onClick: () => redirect(EVENTS) },
                { text: "Blog", onClick: () => redirect(BLOG) },
                { text: "Career", onClick: () => redirect(CAREER) },
                { text: "Contact us", onClick: () => redirect(CONTACT_US) },
              ].map((item, index) => (
                <span
                  key={index}
                  onClick={item.onClick}
                  className="group relative cursor-pointer tracking-widest font-Anton font-outline-black-11 md:text-5xl text-5xl  text-black hover:font-outline-black-2 hover:text-transparent transition-colors duration-300"
                >
                  {item.text}
                  <span
                    className="absolute left-full top-1/2 transform -translate-y-1/2 opacity-0 scale-0 transition-all duration-300 group-hover:opacity-100 group-hover:scale-100 group-hover:translate-x-2 text-yellow-400"
                    style={{
                      fontSize: "24px",
                    }}
                  >
                    ✦
                  </span>
                </span>
              ))}
            </div>

            <div className="flex space-x-4">
              {[
                {
                  href: "https://www.facebook.com/people/Urban-Venue/61551117187653/",
                  icon: (
                    <FacebookOutlinedIcon
                      style={{ fill: "#1877F2" }}
                      fontSize="large"
                    />
                  ),
                },
                {
                  href: "https://www.instagram.com/theurbanvenue/",
                  icon: (
                    <InstagramIcon
                      style={{ fill: "#ff0000" }}
                      fontSize="large"
                    />
                  ),
                },
                {
                  href: "https://www.youtube.com/@theurbanvenue/featured",
                  icon: (
                    <YouTubeIcon style={{ fill: "#ff0000" }} fontSize="large" />
                  ),
                },
                {
                  href: "https://wa.me/9871371364?text=Hi!%20I%20would%20like%20to%20connect.",
                  icon: (
                    <WhatsAppIcon
                      style={{ fill: "#25d366" }}
                      fontSize="large"
                    />
                  ),
                },
                {
                  href: "https://in.pinterest.com/theurbanvenue/",
                  icon: (
                    <PinterestIcon
                      style={{ fill: "#bd081c" }}
                      fontSize="large"
                    />
                  ),
                },
              ].map((item, index) => (
                <Link
                  key={index}
                  to={item.href}
                  target="_blank"
                  className="transform transition-transform duration-300 hover:-translate-y-2"
                >
                  {item.icon}
                </Link>
              ))}
            </div>
          </div>

          <div className="flex-1 flex flex-wrap justify-center items-center">
            <img
              src={team1}
              className=" p-2 w-[40%] h-[45%] rounded-2xl hidden sm:block "
              alt=""
            />
            <img
              src={team2}
              className=" p-2 w-[40%]  h-[45%] rounded-2xl hidden sm:block"
              alt=""
            />
            <img
              src={team3}
              className=" p-2 w-[40%]  h-[45%] rounded-2xl hidden sm:block"
              alt=""
            />
            <img
              src={team4}
              className="  p-2 w-[40%]  h-[45%] rounded-2xl hidden sm:block"
              alt=""
            />
          </div>
        </div>
      }
    </>
  );
}
