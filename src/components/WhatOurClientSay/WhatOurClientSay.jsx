import React, { useState } from "react";
import feedback1 from "../../assets/hardeep-sahiba.png";
import feedback2 from "../../assets/Riya&Kunal.jpg";
import feedback3 from "../../assets/shriya.png";
import feedback4 from "../../assets/Aarav&Meera.jpeg";
import feedback5 from "../../assets/Aanya&Rohan.jpeg";
import feedback6 from "../../assets/Ishita&Karan.jpeg";
import feedback7 from "../../assets/Sanya&Raghav.png";
import feedback8 from "../../assets/Rahul&Priya.jpeg";
import feedback9 from "../../assets/<PERSON>hul&Friends.jpg";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/autoplay";
import { Autoplay, Navigation } from "swiper/modules";

const WhatOurClientSay = () => {
  // Example slides data
  const slides = [
    {
      images: [feedback9],
      head: "<PERSON><PERSON> & Friends, Pool Party",
      text: "We decided to throw a last-minute pool party for my friends, and <PERSON> Venue made it happen in no time. They found us a farmhouse with a stunning pool and set up the whole space with lights, a sound system, and a small food corner. Everything was ready when we arrived, and it looked amazing. The team was super accommodating and made sure we had everything we needed. It felt like a mini vacation, and we didn’t want the day to end! Can’t wait to book with them again.",
    },
    {
      images: [feedback8],
      head: "Rahul & Priya, Cocktail Party",
      text: "Our cocktail party was an absolute blast, and Urban Venue made it all happen effortlessly! They suggested a stunning farmhouse with a stylish lounge setup, dazzling lights, and a well-stocked bar. The DJ kept the energy high all night, and the customized cocktails added a fun personal touch. From the seamless event flow to the delicious appetizers, everything was planned to perfection. We got to let loose, dance, and enjoy every moment with our friends and family. It was the perfect way to kick off our wedding celebrations in style!",
    },
    {
      images: [feedback7],
      head: "Sanya & Raghav, Mehendi Ceremony",
      text: "Our Mehendi ceremony was nothing short of magical, all thanks to Urban Venue! They transformed a beautiful farmhouse into a vibrant, colorful paradise with traditional decor, marigold garlands, and cozy seating for guests. The setup included a live Mehendi artist corner, fun dhol performances, and even a personalized photo booth! The best part? The entire event was stress-free—we just danced, laughed, and enjoyed every moment with our loved ones. It was the perfect start to our wedding celebrations, and we couldn’t have asked for anything better!",
    },
    {
      images: [feedback6],
      head: "Ishita & Karan, Ring Ceremony",
      text: "Our ring ceremony felt straight out of a dream, all thanks to Urban Venue! They helped us find the perfect farmhouse, beautifully decorated with twinkling lights, fresh flowers, and an elegant stage setup. The atmosphere was filled with love, laughter, and music, with a live band adding a magical touch. From the delicious food to the seamless coordination, everything was handled with perfection. We didn’t have to stress about a single thing—just soak in the joy of our special day. Our family and friends are still talking about how beautiful the evening was!",
    },

    {
      images: [feedback5],
      head: "Aanya & Rohan, Haldi Ceremony",
      text: "Our Haldi ceremony was everything we had dreamed of—vibrant, joyful, and full of love. Urban Venue suggested a gorgeous farmhouse with an open garden, where they set up bright yellow decor, fresh floral arrangements, and cozy seating for our guests. The team also arranged fun dhol performances and a playful flower shower that made the celebration even more special. Everything was managed so smoothly that we could just focus on enjoying the moment with our family and friends. It was truly a day filled with laughter, colors, and unforgettable memories!",
    },
    {
      images: [feedback4],
      head: "Aarav & Meera, Engagement Party",
      text: "We wanted our engagement celebration to feel intimate yet lively, and Urban Venue made it happen effortlessly. They suggested a beautiful farmhouse and took care of everything – from dreamy fairy lights to a personalized cocktail bar. The delicious food and live acoustic music added the perfect romantic touch. The best part? We didn't have to stress about a thing. Urban Venue's team handled every detail, so we could enjoy the evening with our loved ones. Our guests are still raving about how magical the night was!",
    },
    {
      images: [feedback3],
      head: "Shriya, 30th Birthday Party",
      text: "I wanted my 30th birthday to be extra special, and Urban Venue nailed it. They recommended a farmhouse with a pool and helped set up everything – from vibrant poolside decor to a fantastic DJ. The catering was delicious, and the team even arranged fun games to keep the vibe going. What stood out the most was how easy they made everything for me. I just showed up and enjoyed myself, stress-free. My friends are still talking about how much fun we had that day!",
    },
    {
      images: [feedback1],
      head: "Hardeep & Sahiba, Wedding Celebration",
      text: "We found Urban Venue while searching for a wedding venue, and it was the best decision we made. Their team helped us pick a beautiful farmhouse that matched our vision of an intimate, elegant outdoor wedding. From the decor to the catering, everything was beautifully executed. The staff was so attentive and made sure we didn’t have to worry about a single thing on the big day. It felt like they genuinely cared about making our wedding perfect. Our families were thrilled, and Urban Venue made our dream wedding come true.",
    },
    {
      images: [feedback2],
      head: "Riya & Kunal,Engagement Celebration",
      text: "We wanted our engagement to feel magical but not over-the-top, and Urban Venue delivered. They suggested a beautiful lawn venue with a cozy setup of fairy lights, candles, and stunning floral arrangements. The team worked with us on every detail, even helping us choose the menu. On the day of the event, everything looked breathtaking, and the service was impeccable. Our guests kept complimenting the food and the atmosphere. Urban Venue made our special day feel truly magical, and we’re so grateful for their hard work!",
    },
  ];

  const [currentSlide, setCurrentSlide] = useState(0);
  const [activeIndex, setActiveIndex] = useState(0);

  const prevSlide = () => {
    setCurrentSlide((prevIndex) =>
      prevIndex === 0 ? slides.length - 1 : prevIndex - 1
    );
  };

  const nextSlide = () => {
    setCurrentSlide((prevIndex) =>
      prevIndex === slides.length - 1 ? 0 : prevIndex + 1
    );
  };

  return (
    // <div className="w-screen sm:mt-0 mt-[20px] sm:h-screen flex flex-col items-center justify-center bg-gray-100">
    //   <div className="relative flex items-center justify-between w-11/12 h-5/6 shadow-lg rounded-lg overflow-hidden bg-[#f1cf76] p-4">
    //     <div className="flex sm:flex-row flex-col justify-center items-center w-full h-full">
    //       {/* Swiper slider for the images */}
    //       <div className="sm:w-1/2 pt-6 pb-6 h-full w-full">
    //         <Swiper
    //           modules={[Autoplay, Navigation]}
    //           autoplay={{ delay: 3000, disableOnInteraction: false }}
    //           navigation
    //           loop={true}
    //           spaceBetween={10}
    //           slidesPerView={1}
    //           speed={500}
    //           className="w-full h-full"
    //         >
    //           {slides[currentSlide].images.map((image, index) => (
    //             <SwiperSlide key={index} className="rounded-lg overflow-hidden">
    //               <img
    //                 src={image}
    //                 alt={`Slide ${index + 1}`}
    //                 className="object-contain w-full sm:h-full h-[360px]"
    //               />
    //             </SwiperSlide>
    //           ))}
    //         </Swiper>
    //       </div>

    //       {/* Text content */}
    //       <div className="sm:w-1/2 w-full flex flex-col justify-start sm:p-8 p-0">
    //         <h2 className="xl:text-6xl md:text-4xl text-2xl font-bold mb-4 text-transparent font-outline-black-1">
    //           {slides[currentSlide].head}
    //         </h2>
    //         <p className="text-lg text-gray-700">{slides[currentSlide].text}</p>
    //       </div>
    //     </div>
    //   </div>

    //   {/* Navigation buttons */}
    //   <div className="flex justify-center space-x-8 mt-4 mb-[20px]">
    //     <button
    //       onClick={prevSlide}
    //       className="text-xl text-white bg-gray-700 px-6 py-2 rounded-full shadow-md hover:bg-gray-800 transition"
    //     >
    //       Prev
    //     </button>
    //     <button
    //       onClick={nextSlide}
    //       className="text-xl text-white bg-gray-700 px-6 py-2 rounded-full shadow-md hover:bg-gray-800 transition"
    //     >
    //       Next
    //     </button>
    //   </div>
    // </div>
    <div className="relative w-screen sm:mt-0 mt-[20px] sm:h-screen flex flex-col items-center justify-center bg-gray-100">
      <div className="relative w-11/12 h-5/6 shadow-lg rounded-lg overflow-hidden bg-[#f1cf76] p-4">
        <Swiper
          modules={[Autoplay, Navigation]}
          autoplay={{ delay: 3000, disableOnInteraction: false }}
          navigation={true} // ✅ Uses Swiper's built-in next/prev buttons
          loop={true}
          spaceBetween={10}
          slidesPerView={1}
          speed={500}
          className="w-full h-full"
        >
          {slides.map((slide, index) => (
            <SwiperSlide
              key={index}
              className="flex sm:flex-row flex-col justify-center items-center w-full h-full"
            >
              {/* Image Section */}
              <div className="sm:w-1/2 w-full flex justify-center">
                <img
                  src={slide.images[0]} // Assuming each slide has at least one image
                  alt={`Slide ${index + 1}`}
                  className="object-contain w-full sm:h-[500px] h-[360px] rounded-lg"
                />
              </div>

              {/* Text Section */}
              <div className="sm:w-1/2 w-full flex flex-col justify-start sm:p-8 p-0">
                <h2 className="xl:text-6xl md:text-4xl text-2xl font-bold mb-4">
                  {slide.head}
                </h2>
                <p className="text-lg text-gray-700">{slide.text}</p>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
};

export default WhatOurClientSay;
