/**
 * Image optimization utilities for better performance
 */

/**
 * Preload images in the background
 * @param {Array} imageSources - Array of image URLs to preload
 * @param {Function} onProgress - Callback for progress updates
 * @param {Function} onComplete - Callback when all images are loaded
 */
export const preloadImages = (imageSources, onProgress, onComplete) => {
  if (!imageSources || imageSources.length === 0) {
    onComplete && onComplete([]);
    return;
  }

  const loadedImages = [];
  let loadedCount = 0;
  const totalImages = imageSources.length;

  imageSources.forEach((src, index) => {
    const img = new Image();
    
    img.onload = () => {
      loadedImages[index] = { src, loaded: true, error: false };
      loadedCount++;
      
      // Report progress
      onProgress && onProgress({
        loaded: loadedCount,
        total: totalImages,
        percentage: Math.round((loadedCount / totalImages) * 100)
      });

      // Check if all images are loaded
      if (loadedCount === totalImages) {
        onComplete && onComplete(loadedImages);
      }
    };

    img.onerror = () => {
      loadedImages[index] = { src, loaded: false, error: true };
      loadedCount++;
      
      // Report progress even for errors
      onProgress && onProgress({
        loaded: loadedCount,
        total: totalImages,
        percentage: Math.round((loadedCount / totalImages) * 100)
      });

      // Check if all images are processed
      if (loadedCount === totalImages) {
        onComplete && onComplete(loadedImages);
      }
    };

    img.src = src;
  });
};

/**
 * Create optimized image URL with size hints
 * @param {string} src - Original image URL
 * @param {Object} options - Optimization options
 */
export const optimizeImageUrl = (src, options = {}) => {
  if (!src) return src;

  const {
    width,
    height,
    quality = 80,
    format = 'auto'
  } = options;

  // If it's a localhost URL, return as-is for development
  if (src.includes('localhost')) {
    return src;
  }

  // For production, you could add image optimization service URLs here
  // Example: Cloudinary, ImageKit, etc.
  
  return src;
};

/**
 * Get responsive image sizes based on viewport
 */
export const getResponsiveImageSizes = () => {
  const width = window.innerWidth;
  
  if (width < 640) {
    return { width: 340, height: 294 }; // Mobile
  } else if (width < 1024) {
    return { width: 400, height: 350 }; // Tablet
  } else {
    return { width: 500, height: 400 }; // Desktop
  }
};

/**
 * Batch preload images with priority
 * @param {Array} images - Array of image objects with src and priority
 * @param {Object} options - Options for preloading
 */
export const batchPreloadImages = (images, options = {}) => {
  const {
    highPriorityCount = 3,
    onHighPriorityComplete,
    onAllComplete,
    onProgress
  } = options;

  // Sort by priority (higher priority first)
  const sortedImages = [...images].sort((a, b) => (b.priority || 0) - (a.priority || 0));
  
  // Split into high and low priority
  const highPriorityImages = sortedImages.slice(0, highPriorityCount);
  const lowPriorityImages = sortedImages.slice(highPriorityCount);

  let highPriorityLoaded = false;
  let allLoaded = false;

  // Preload high priority images first
  preloadImages(
    highPriorityImages.map(img => img.src),
    (progress) => {
      onProgress && onProgress({
        ...progress,
        phase: 'high-priority'
      });
    },
    (results) => {
      highPriorityLoaded = true;
      onHighPriorityComplete && onHighPriorityComplete(results);

      // Start loading low priority images
      if (lowPriorityImages.length > 0) {
        preloadImages(
          lowPriorityImages.map(img => img.src),
          (progress) => {
            onProgress && onProgress({
              ...progress,
              phase: 'low-priority'
            });
          },
          (lowPriorityResults) => {
            allLoaded = true;
            onAllComplete && onAllComplete([...results, ...lowPriorityResults]);
          }
        );
      } else {
        allLoaded = true;
        onAllComplete && onAllComplete(results);
      }
    }
  );

  return {
    isHighPriorityLoaded: () => highPriorityLoaded,
    isAllLoaded: () => allLoaded
  };
};

/**
 * Create intersection observer for lazy loading
 * @param {Function} callback - Callback when element intersects
 * @param {Object} options - Intersection observer options
 */
export const createLazyLoadObserver = (callback, options = {}) => {
  const defaultOptions = {
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  };

  return new IntersectionObserver(callback, defaultOptions);
};

/**
 * Check if image is already cached
 * @param {string} src - Image URL
 */
export const isImageCached = (src) => {
  const img = new Image();
  img.src = src;
  return img.complete && img.naturalHeight !== 0;
};
