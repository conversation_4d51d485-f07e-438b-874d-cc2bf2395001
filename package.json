{"name": "urban", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^4.1.3", "@mui/icons-material": "^6.1.9", "@mui/material": "^6.1.6", "bootstrap": "^5.3.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "emailjs-com": "^3.2.0", "flowbite": "^2.5.2", "flowbite-react": "^0.10.2", "framer-motion": "^11.11.15", "lucide-react": "^0.453.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-router-dom": "^6.27.0", "swiper": "^11.1.15", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.16", "vite": "^6.2.1", "vite-plugin-static-copy": "^2.2.0"}}